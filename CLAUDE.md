# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Root Level Commands
- `npm run test:e2e` - Run Playwright end-to-end tests across the application
- `npm run test:e2e:ui` - Run Playwright tests with UI mode for debugging

### API Backend (FastAPI - Python)
**Location**: `apps/api/`
- `cd apps/api && poetry install` - Install Python dependencies
- `cd apps/api && poetry run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000` - Start development server
- `cd apps/api && poetry run pytest` - Run API tests
- `cd apps/api && poetry run pytest tests/ -v` - Run tests with verbose output
- `cd apps/api && poetry run black .` - Format code with Black
- `cd apps/api && poetry run ruff check .` - Lint code with Ruff
- `cd apps/api && poetry run mypy .` - Type check with MyPy

### Web Frontend (Next.js - TypeScript)
**Location**: `apps/web/`
- `cd apps/web && npm install` - Install dependencies
- `cd apps/web && npm run dev` - Start development server (with Turbopack)
- `cd apps/web && npm run build` - Build for production
- `cd apps/web && npm run start` - Start production server
- `cd apps/web && npm run lint` - Run ESLint
- `cd apps/web && npm run test` - Run Jest unit tests
- `cd apps/web && npm run test:ci` - Run tests in CI mode with coverage
- `cd apps/web && npm run test:e2e` - Run Playwright E2E tests
- `cd apps/web && npm run storybook` - Start Storybook for component development

### Infrastructure (AWS CDK - Python)
**Location**: `infrastructure/`
- `cd infrastructure && pip install -r requirements.txt` - Install CDK dependencies
- `cd infrastructure && cdk deploy ModernAction-dev` - Deploy development stack
- `cd infrastructure && cdk deploy ModernAction-staging` - Deploy staging stack
- `cd infrastructure && cdk deploy ModernAction-prod` - Deploy production stack
- `cd infrastructure && cdk diff ModernAction-dev` - Show changes to be deployed

## Architecture Overview

### Monorepo Structure
This is a modern civic engagement platform built as a monorepo with three main applications:

1. **API Backend** (`apps/api/`) - FastAPI application with PostgreSQL database
2. **Web Frontend** (`apps/web/`) - Next.js React application with Auth0 authentication
3. **Infrastructure** (`infrastructure/`) - AWS CDK for cloud deployment
4. **Lambda Functions** (`apps/lambda/`) - Serverless background jobs

### Key Technologies
- **Backend**: FastAPI, SQLAlchemy, PostgreSQL, Redis, Celery
- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS, Auth0
- **Infrastructure**: AWS (ECS Fargate, RDS, Lambda, SQS, ALB), CDK
- **Testing**: Playwright (E2E), Jest (Unit), Pytest (API)
- **AI/ML**: Transformers, Torch, HuggingFace for bill summarization

### Database Models
Core entities include:
- **User**: Auth0-integrated user management
- **Campaign**: Political campaigns users can participate in
- **Bill**: Legislative bills with AI-generated summaries
- **Action**: User actions (emails, calls, tweets) on campaigns
- **Official**: Elected officials with contact information

### External APIs Integration
- **OpenStates API**: Legislative data and official information
- **Google Civic Info API**: Official contact information
- **Congress.gov API**: Federal legislative data
- **Propublica Congress API**: Additional legislative data
- **Auth0**: Authentication and user management
- **AWS SES**: Email delivery for campaigns

### Authentication & Authorization
Uses Auth0 for authentication with JWT tokens. The web app handles Auth0 callbacks at `/api/auth/*` while the FastAPI backend provides protected endpoints at `/api/v1/*`.

### Deployment Architecture
- **Development**: Local Docker Compose setup
- **Staging/Production**: AWS ECS Fargate with Application Load Balancer
- **Database**: AWS RDS PostgreSQL with automated backups
- **Background Jobs**: AWS Lambda functions triggered by SQS/CloudWatch Events
- **CI/CD**: AWS CodeBuild with automated ECR deployments

### Key Configuration Files
- `apps/api/app/core/config.py` - API configuration and environment variables
- `apps/web/next.config.ts` - Next.js configuration
- `infrastructure/modernaction/modernaction_stack.py` - AWS infrastructure definition
- `apps/api/pyproject.toml` - Python dependencies and tooling configuration

### Development Workflow
1. Backend changes require running both linting (`ruff`) and formatting (`black`)
2. Frontend changes should be tested with both unit tests (`jest`) and E2E tests (`playwright`)
3. Database migrations are handled through Alembic in `apps/api/alembic/`
4. Infrastructure changes require CDK diff before deployment

### Common Patterns
- API endpoints follow REST conventions under `/api/v1/`
- React components use TypeScript interfaces from `src/types/`
- Database models use SQLAlchemy ORM with Pydantic schemas for validation
- Error handling uses FastAPI's built-in HTTP exceptions
- State management in frontend uses Zustand for complex state