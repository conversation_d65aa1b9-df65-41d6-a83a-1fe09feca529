# app/api/v1/api.py
from fastapi import APIRouter
from app.api.v1.endpoints import health, officials, bills, campaigns, actions, ai, admin

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(health.router, tags=["health"])
api_router.include_router(officials.router, prefix="/officials", tags=["officials"])
api_router.include_router(bills.router, prefix="/bills", tags=["bills"])
api_router.include_router(campaigns.router, prefix="/campaigns", tags=["campaigns"])
api_router.include_router(actions.router, prefix="/actions", tags=["actions"])
api_router.include_router(ai.router, prefix="/ai", tags=["ai"])
api_router.include_router(admin.router, prefix="/admin", tags=["admin"])
