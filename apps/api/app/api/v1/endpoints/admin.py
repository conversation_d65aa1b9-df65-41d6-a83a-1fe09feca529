# app/api/v1/endpoints/admin.py
"""
Admin endpoints for testing and management
"""

from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import HTMLResponse
from sqlalchemy.orm import Session
from typing import List, Optional
import asyncio

from app.db.database import get_db
from app.services.congress_gov_api import CongressGovAPI
from app.services.ai import summarize_bill
from app.services.ai_service import AIService
from app.services.enhanced_bill_service import EnhancedBillService
from app.models.bill import Bill, BillType, BillStatus
from app.schemas.bill import BillResponse, BillCreate
import time
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/status")
async def get_system_status():
    """Get system status for all services"""
    try:
        # Check Congress API
        congress_api = CongressGovAPI()
        congress_status = {
            "enabled": congress_api.enabled,
            "message": "API key configured" if congress_api.enabled else "Missing CONGRESS_GOV_API_KEY"
        }

        # Check Hugging Face AI
        try:
            from app.services.ai import get_summarizer
            summarizer = get_summarizer()
            hf_status = {
                "enabled": summarizer is not None,
                "message": "Transformers available" if summarizer else "Transformers not installed"
            }
        except Exception as e:
            hf_status = {"enabled": False, "message": f"Error: {str(e)}"}

        # Check OpenAI AI
        try:
            ai_service = AIService()
            openai_status = {
                "enabled": ai_service.enabled,
                "message": "API key configured" if ai_service.enabled else "Missing OPENAI_API_KEY"
            }
        except Exception as e:
            openai_status = {"enabled": False, "message": f"Error: {str(e)}"}

        return {
            "Congress.gov API": congress_status,
            "Hugging Face AI": hf_status,
            "OpenAI AI": openai_status
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error checking system status: {str(e)}")

@router.get("/test-bill-lookup")
async def test_bill_lookup(congress: int, bill_type: str, bill_number: int):
    """Test bill lookup from Congress.gov API"""
    try:
        congress_api = CongressGovAPI()
        if not congress_api.enabled:
            return {"success": False, "error": "Congress.gov API not enabled"}

        bill_data = congress_api.get_bill_by_number(congress, bill_type, bill_number)
        if bill_data:
            return {"success": True, "bill": bill_data}
        else:
            return {"success": False, "error": "Bill not found"}
    except Exception as e:
        return {"success": False, "error": str(e)}

@router.post("/test-ai-summary")
async def test_ai_summary(request: dict):
    """Test AI summarization"""
    try:
        bill_text = request.get("bill_text", "")
        bill_title = request.get("bill_title", "")

        if not bill_text.strip():
            return {"success": False, "error": "Bill text is required"}

        start_time = time.time()

        # Try OpenAI first, then fall back to Hugging Face
        try:
            ai_service = AIService()
            if ai_service.enabled:
                result = await ai_service.process_bill_complete(bill_text, {"title": bill_title})
                summary = result.get("ai_summary", "No summary generated")
            else:
                raise Exception("OpenAI not available")
        except Exception:
            # Fall back to Hugging Face
            from app.services.ai import summarize_bill
            summary = summarize_bill(bill_text, bill_title)

        processing_time = round(time.time() - start_time, 2)

        return {
            "success": True,
            "summary": summary,
            "processing_time": processing_time
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@router.get("/recent-bills")
async def get_recent_bills():
    """Get recent bills from Congress.gov API"""
    try:
        congress_api = CongressGovAPI()
        if not congress_api.enabled:
            return {"success": False, "error": "Congress.gov API not enabled"}

        bills = congress_api.get_recent_bills(limit=10)
        return {"success": True, "bills": bills}
    except Exception as e:
        return {"success": False, "error": str(e)}

@router.delete("/clear-bills")
async def clear_bills(db: Session = Depends(get_db)):
    """Clear all bills from database"""
    try:
        deleted_count = db.query(Bill).count()
        db.query(Bill).delete()
        db.commit()
        return {"success": True, "deleted_count": deleted_count}
    except Exception as e:
        db.rollback()
        return {"success": False, "error": str(e)}

@router.delete("/clear-test-bills")
async def clear_test_bills(db: Session = Depends(get_db)):
    """Clear specific test bills created during E2E testing"""
    try:
        # Delete specific test bills by bill number
        test_bills = ["HR10", "HR15", "HR20", "S1"]
        deleted_count = 0

        for bill_number in test_bills:
            bill = db.query(Bill).filter(Bill.bill_number == bill_number).first()
            if bill:
                db.delete(bill)
                deleted_count += 1

        db.commit()
        return {"success": True, "deleted_count": deleted_count, "test_bills_removed": test_bills}
    except Exception as e:
        db.rollback()
        return {"success": False, "error": str(e)}

@router.post("/summarize-bill")
async def summarize_bill_from_congress(congress: int, bill_type: str, bill_number: int):
    """Fetch a bill from Congress.gov and generate AI summary"""
    try:
        # Fetch bill data
        congress_api = CongressGovAPI()
        if not congress_api.enabled:
            return {"success": False, "error": "Congress.gov API not enabled"}

        bill_data = congress_api.get_bill_by_number(congress, bill_type, bill_number)
        if not bill_data:
            return {"success": False, "error": "Bill not found"}

        # Get comprehensive bill text for AI processing
        bill_text = f"BILL TITLE: {bill_data.get('title', '')}\n\n"

        # Add bill summary if available
        if bill_data.get('summary'):
            bill_text += f"OFFICIAL SUMMARY: {bill_data['summary']}\n\n"

        # Add sponsor information
        if bill_data.get('sponsors') and len(bill_data['sponsors']) > 0:
            sponsor = bill_data['sponsors'][0]
            bill_text += f"SPONSOR: {sponsor.get('fullName', 'Unknown')} [{sponsor.get('party', '')}-{sponsor.get('state', '')}]\n\n"

        # Add latest action
        if bill_data.get('latestAction'):
            bill_text += f"LATEST ACTION: {bill_data['latestAction'].get('text', '')}\n\n"

        # Get the ACTUAL FULL TEXT of the bill
        try:
            logger.info(f"Fetching full text for {bill_type.upper()}.{bill_number}")
            full_text = await congress_api.get_bill_full_text(congress, bill_type, bill_number)

            if full_text and len(full_text.strip()) > 100:  # Ensure we got substantial content
                bill_text += f"FULL BILL TEXT:\n{full_text}"
                logger.info(f"Successfully retrieved {len(full_text)} characters of full bill text")
            else:
                # Fallback to metadata-based analysis
                bill_text += "BILL CONTENT: [Full text not available - analysis based on title, summary, and metadata]"
                logger.warning("Full text not available, using metadata only")

        except Exception as e:
            logger.error(f"Error fetching full bill text: {e}")
            bill_text += f"BILL CONTENT: [Text retrieval error: {str(e)} - analysis based on available metadata]"

        start_time = time.time()

        # Try OpenAI first, then fall back to Hugging Face
        ai_result = None
        ai_method = "unknown"

        try:
            ai_service = AIService()
            if ai_service.enabled:
                # For now, just get the summary directly to debug
                summary = await ai_service._generate_summary(bill_text, bill_data)
                ai_method = "OpenAI GPT-4"

                # Try to get full analysis
                try:
                    ai_result = await ai_service.process_bill_complete(bill_text, bill_data)
                except Exception as e:
                    logger.warning(f"Full AI analysis failed, using summary only: {e}")
                    ai_result = {"ai_summary": summary}
            else:
                raise Exception("OpenAI not available")
        except Exception as e:
            logger.error(f"OpenAI processing failed: {e}")
            # Fall back to Hugging Face
            from app.services.ai import summarize_bill
            summary = summarize_bill(bill_text, bill_data.get('title', ''))
            ai_method = "Hugging Face Transformers"

        processing_time = round(time.time() - start_time, 2)

        return {
            "success": True,
            "bill": {
                "congress": congress,
                "bill_type": bill_type.upper(),
                "bill_number": bill_number,
                "title": bill_data.get('title', 'Unknown'),
                "introduced_date": bill_data.get('introducedDate', 'Unknown'),
                "latest_action": bill_data.get('latestAction', {}).get('text', 'No status'),
                "sponsor": bill_data.get('sponsors', [{}])[0].get('fullName', 'Unknown') if bill_data.get('sponsors') else 'Unknown'
            },
            "ai_summary": summary,
            "ai_method": ai_method,
            "processing_time": processing_time,
            "full_ai_result": ai_result if ai_result else None
        }
    except Exception as e:
        return {"success": False, "error": str(e)}


@router.post("/process-and-save-bill")
async def process_and_save_bill(
    congress: int = 118,
    bill_type: str = "hr",
    bill_number: int = 1,
    db: Session = Depends(get_db)
):
    """
    Complete bill processing pipeline: fetch, analyze, and save to database.

    This endpoint tests the entire flow:
    1. Fetch bill data from Congress.gov API
    2. Get full bill text
    3. Generate AI analysis
    4. Save everything to database
    5. Return database record
    """
    start_time = time.time()

    try:
        logger.info(f"Starting complete bill processing for {bill_type.upper()}.{bill_number}")

        # Initialize enhanced bill service
        enhanced_bill_service = EnhancedBillService()

        # Use the enhanced service to process the complete bill
        bill_number_str = f"{bill_type.upper()}{bill_number}"
        result = await enhanced_bill_service.process_bill(
            bill_number_str, str(congress)
        )

        processing_time = time.time() - start_time

        # Check if processing was successful
        if result.get("status") == "success":
            return {
                "success": True,
                "message": result.get("message", f"Successfully processed and saved bill {bill_type.upper()}.{bill_number}"),
                "bill_id": result["bill_id"],
                "processing_time": round(processing_time, 2),
                "steps_completed": {
                    "metadata_fetched": True,
                    "full_text_fetched": True,
                    "ai_analysis_generated": True,
                    "database_saved": True
                },
                "bill_data": {
                    "id": result["bill_id"],
                    "title": result.get("title", ""),
                    "bill_number": result["bill_number"],
                    "ai_summary": result.get("ai_summary", ""),
                    "support_reasons": result.get("support_reasons", []),
                    "oppose_reasons": result.get("oppose_reasons", []),
                    "ai_tags": result.get("tags", []),
                    "full_text_length": result.get("full_text_length", 0),
                    "official_bill_url": None,
                    "ai_processed_at": None
                }
            }
        elif result.get("status") == "already_processed":
            return {
                "success": True,
                "message": result.get("message", f"Bill {bill_type.upper()}.{bill_number} was already processed"),
                "bill_id": result["bill_id"],
                "processing_time": round(processing_time, 2),
                "steps_completed": {
                    "metadata_fetched": True,
                    "full_text_fetched": True,
                    "ai_analysis_generated": True,
                    "database_saved": True
                },
                "bill_data": {
                    "id": result["bill_id"],
                    "title": "",
                    "bill_number": result.get("bill_number", f"{bill_type.upper()}.{bill_number}"),
                    "ai_summary": "",
                    "support_reasons": None,
                    "oppose_reasons": None,
                    "ai_tags": [],
                    "full_text_length": 0,
                    "official_bill_url": None,
                    "ai_processed_at": None
                }
            }
        else:
            # Error case
            return {
                "success": False,
                "error": result.get("error", "Unknown error occurred"),
                "message": result.get("message", f"Failed to process bill {bill_type.upper()}.{bill_number}"),
                "processing_time": round(processing_time, 2)
            }

    except Exception as e:
        logger.error(f"Complete bill processing failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {"success": False, "error": str(e)}


@router.get("/database-bills")
async def get_database_bills(db: Session = Depends(get_db)):
    """Get all bills from the database to verify storage"""
    try:
        bills = db.query(Bill).order_by(Bill.created_at.desc()).limit(10).all()

        bill_data = []
        for bill in bills:
            bill_data.append({
                "id": str(bill.id),
                "title": bill.title,
                "bill_number": bill.bill_number,
                "congress_gov_id": bill.congress_gov_id,
                "official_bill_url": bill.official_bill_url,
                "ai_summary": bill.ai_summary,
                "support_reasons": bill.support_reasons,
                "oppose_reasons": bill.oppose_reasons,
                "amend_reasons": bill.amend_reasons,
                "ai_tags": bill.ai_tags,
                "environmental_threat_analysis": bill.environmental_threat_analysis,
                "social_rights_threat_analysis": bill.social_rights_threat_analysis,
                "environmental_justice_threat_analysis": bill.environmental_justice_threat_analysis,
                "full_text_length": len(bill.full_text) if bill.full_text else 0,
                "ai_processed_at": bill.ai_processed_at.isoformat() if bill.ai_processed_at else None,
                "created_at": bill.created_at.isoformat() if bill.created_at else None
            })

        return {
            "success": True,
            "count": len(bill_data),
            "bills": bill_data
        }

    except Exception as e:
        logger.error(f"Database query failed: {e}")
        return {"success": False, "error": str(e)}


@router.get("/", response_class=HTMLResponse)
async def admin_dashboard():
    """Admin dashboard for testing bill processing"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>ModernAction.io - Admin Dashboard</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
            h2 { color: #34495e; margin-top: 30px; }
            .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background: #fafafa; }
            .form-group { margin: 15px 0; }
            label { display: block; margin-bottom: 5px; font-weight: bold; color: #555; }
            input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; }
            button { background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; margin: 5px; }
            button:hover { background: #2980b9; }
            .success { color: #27ae60; background: #d5f4e6; padding: 10px; border-radius: 4px; margin: 10px 0; }
            .error { color: #e74c3c; background: #fdf2f2; padding: 10px; border-radius: 4px; margin: 10px 0; }
            .result { background: #ecf0f1; padding: 15px; border-radius: 4px; margin: 10px 0; white-space: pre-wrap; }
            .status { padding: 5px 10px; border-radius: 3px; font-size: 12px; font-weight: bold; }
            .status.working { background: #f39c12; color: white; }
            .status.success { background: #27ae60; color: white; }
            .status.error { background: #e74c3c; color: white; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🏛️ ModernAction.io - Admin Dashboard</h1>
            <p>Test and manage bill processing functionality</p>
            
            <div class="section">
                <h2>📊 System Status</h2>
                <div id="system-status">Loading...</div>
                <button onclick="checkSystemStatus()">Refresh Status</button>
            </div>
            
            <div class="section">
                <h2>🔍 Test Bill Lookup</h2>
                <div class="form-group">
                    <label>Congress Number:</label>
                    <input type="number" id="congress" value="118" min="100" max="120">
                </div>
                <div class="form-group">
                    <label>Bill Type:</label>
                    <select id="bill-type">
                        <option value="hr">House Bill (HR)</option>
                        <option value="s">Senate Bill (S)</option>
                        <option value="hjres">House Joint Resolution (HJRES)</option>
                        <option value="sjres">Senate Joint Resolution (SJRES)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Bill Number:</label>
                    <input type="number" id="bill-number" value="1" min="1">
                </div>
                <button onclick="testBillLookup()">Fetch Bill Data</button>
                <div id="bill-lookup-result"></div>
            </div>

            <div class="section">
                <h2>🔄 Complete Bill Processing Pipeline</h2>
                <p>Test the complete flow: fetch bill → get full text → AI analysis → database storage</p>
                <button onclick="testCompletePipeline()" style="background: #e74c3c; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold;">🚀 Test Complete Pipeline</button>
                <div id="complete-pipeline-result"></div>
            </div>

            <div class="section">
                <h2>💾 Database Records</h2>
                <p>View bills stored in the database</p>
                <button onclick="loadDatabaseRecords()" style="background: #27ae60; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold;">📊 Load Database Records</button>
                <div id="database-records"></div>
            </div>

            <div class="section">
                <h2>🤖 Test AI Summarization</h2>
                <div class="form-group">
                    <label>Bill Text to Summarize:</label>
                    <textarea id="bill-text" rows="6" placeholder="Enter bill text here or use 'Fetch Bill Data' above first..."></textarea>
                </div>
                <div class="form-group">
                    <label>Bill Title (optional):</label>
                    <input type="text" id="bill-title" placeholder="Bill title for context">
                </div>
                <button onclick="testAISummarization()">Generate AI Summary</button>
                <div id="ai-summary-result"></div>
            </div>
            
            <div class="section">
                <h2>📋 Recent Bills</h2>
                <button onclick="fetchRecentBills()">Fetch Recent Bills</button>
                <div id="recent-bills-result"></div>
            </div>

            <div class="section">
                <h2>📝 AI Summary Results</h2>
                <div id="ai-summary-results" style="max-height: 400px; overflow-y: auto;">
                    <p style="color: #666; font-style: italic;">AI summaries will appear here when you click "Summarize" on bills above.</p>
                </div>
                <button onclick="clearSummaryResults()" style="background: #95a5a6;">Clear Results</button>
            </div>
            
            <div class="section">
                <h2>💾 Database Bills</h2>
                <button onclick="fetchDatabaseBills()">View Database Bills</button>
                <button onclick="clearDatabase()" style="background: #e74c3c;">Clear All Bills</button>
                <div id="database-bills-result"></div>
            </div>
        </div>
        
        <script>
            async function checkSystemStatus() {
                const statusDiv = document.getElementById('system-status');
                statusDiv.innerHTML = 'Checking...';
                
                try {
                    const response = await fetch('/api/v1/admin/status');
                    const data = await response.json();
                    
                    let html = '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">';
                    
                    for (const [service, status] of Object.entries(data)) {
                        const statusClass = status.enabled ? 'success' : 'error';
                        html += `<div><strong>${service}:</strong> <span class="status ${statusClass}">${status.enabled ? 'ENABLED' : 'DISABLED'}</span>`;
                        if (status.message) html += `<br><small>${status.message}</small>`;
                        html += '</div>';
                    }
                    
                    html += '</div>';
                    statusDiv.innerHTML = html;
                } catch (error) {
                    statusDiv.innerHTML = `<div class="error">Error checking status: ${error.message}</div>`;
                }
            }
            
            async function testBillLookup() {
                const congress = document.getElementById('congress').value;
                const billType = document.getElementById('bill-type').value;
                const billNumber = document.getElementById('bill-number').value;
                const resultDiv = document.getElementById('bill-lookup-result');
                
                resultDiv.innerHTML = 'Fetching bill data...';
                
                try {
                    const response = await fetch(`/api/v1/admin/test-bill-lookup?congress=${congress}&bill_type=${billType}&bill_number=${billNumber}`);
                    const data = await response.json();
                    
                    if (data.success) {
                        const bill = data.bill;
                        document.getElementById('bill-text').value = bill.title + '\\n\\n' + (bill.summary || 'No summary available');
                        document.getElementById('bill-title').value = bill.title;
                        
                        resultDiv.innerHTML = `
                            <div class="success">✅ Bill found successfully!</div>
                            <div class="result">
                                <strong>Title:</strong> ${bill.title}
                                <strong>Status:</strong> ${bill.latestAction?.text || 'No status'}
                                <strong>Introduced:</strong> ${bill.introducedDate || 'Unknown'}
                                <strong>Summary:</strong> ${bill.summary || 'No summary available'}
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ ${data.error}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                }
            }
            
            async function testAISummarization() {
                const billText = document.getElementById('bill-text').value;
                const billTitle = document.getElementById('bill-title').value;
                const resultDiv = document.getElementById('ai-summary-result');
                
                if (!billText.trim()) {
                    resultDiv.innerHTML = '<div class="error">❌ Please enter bill text to summarize</div>';
                    return;
                }
                
                resultDiv.innerHTML = 'Generating AI summary...';
                
                try {
                    const response = await fetch('/api/v1/admin/test-ai-summary', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ bill_text: billText, bill_title: billTitle })
                    });
                    const data = await response.json();
                    
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="success">✅ AI summary generated successfully!</div>
                            <div class="result">
                                <strong>Summary:</strong>
                                ${data.summary}
                                
                                <strong>Processing Time:</strong> ${data.processing_time}s
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ ${data.error}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                }
            }
            
            async function fetchRecentBills() {
                const resultDiv = document.getElementById('recent-bills-result');
                resultDiv.innerHTML = 'Fetching recent bills...';

                try {
                    const response = await fetch('/api/v1/admin/recent-bills');
                    const data = await response.json();

                    if (data.success) {
                        let html = `<div class="success">✅ Found ${data.bills.length} recent bills</div>`;
                        html += '<div style="display: grid; gap: 15px; margin-top: 15px;">';

                        data.bills.forEach((bill, index) => {
                            // Extract bill info from the number (e.g., "HR1234" -> type="hr", number="1234")
                            const billMatch = bill.number.match(/^([A-Z]+)(\\d+)$/);
                            const billType = billMatch ? billMatch[1].toLowerCase() : 'hr';
                            const billNumber = billMatch ? billMatch[2] : index + 1;

                            html += `
                                <div style="border: 1px solid #ddd; padding: 15px; border-radius: 5px; background: white;">
                                    <div style="display: flex; justify-content: between; align-items: start; gap: 10px;">
                                        <div style="flex: 1;">
                                            <strong style="color: #2c3e50;">${bill.number}</strong>: ${bill.title}
                                            <br><small style="color: #666;">Congress: 118 | Type: ${billType.toUpperCase()}</small>
                                        </div>
                                        <button onclick="summarizeBill(118, '${billType}', ${billNumber}, '${bill.number}', this)"
                                                style="background: #27ae60; white-space: nowrap; min-width: 100px;">
                                            📝 Summarize
                                        </button>
                                    </div>
                                </div>
                            `;
                        });

                        html += '</div>';
                        resultDiv.innerHTML = html;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ ${data.error}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                }
            }
            
            async function fetchDatabaseBills() {
                const resultDiv = document.getElementById('database-bills-result');
                resultDiv.innerHTML = 'Fetching database bills...';
                
                try {
                    const response = await fetch('/api/v1/admin/database-bills');
                    const data = await response.json();
                    
                    if (data.success) {
                        let html = `<div class="success">✅ Found ${data.bills.length} bills in database</div>`;
                        if (data.bills.length > 0) {
                            html += '<div class="result">';
                            data.bills.forEach((bill, index) => {
                                html += `${index + 1}. <strong>${bill.bill_number}</strong>: ${bill.title}\\n`;
                                if (bill.ai_summary) html += `   📝 AI Summary: ${bill.ai_summary.substring(0, 100)}...\\n`;
                            });
                            html += '</div>';
                        }
                        resultDiv.innerHTML = html;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ ${data.error}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                }
            }
            
            async function clearDatabase() {
                if (!confirm('Are you sure you want to clear all bills from the database?')) return;

                const resultDiv = document.getElementById('database-bills-result');
                resultDiv.innerHTML = 'Clearing database...';

                try {
                    const response = await fetch('/api/v1/admin/clear-bills', { method: 'DELETE' });
                    const data = await response.json();

                    if (data.success) {
                        resultDiv.innerHTML = `<div class="success">✅ Cleared ${data.deleted_count} bills from database</div>`;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ ${data.error}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                }
            }

            async function summarizeBill(congress, billType, billNumber, displayName, buttonElement) {
                const originalText = buttonElement.innerHTML;
                buttonElement.innerHTML = '⏳ Processing...';
                buttonElement.disabled = true;

                try {
                    const response = await fetch(`/api/v1/admin/summarize-bill?congress=${congress}&bill_type=${billType}&bill_number=${billNumber}`, {
                        method: 'POST'
                    });
                    const data = await response.json();

                    if (data.success) {
                        // Add result to the AI Summary Results section
                        addSummaryResult(data);

                        buttonElement.innerHTML = '✅ Done';
                        buttonElement.style.background = '#27ae60';

                        // Reset button after 3 seconds
                        setTimeout(() => {
                            buttonElement.innerHTML = originalText;
                            buttonElement.style.background = '#27ae60';
                            buttonElement.disabled = false;
                        }, 3000);
                    } else {
                        buttonElement.innerHTML = '❌ Failed';
                        buttonElement.style.background = '#e74c3c';

                        // Show error in results
                        addSummaryResult({
                            success: false,
                            error: data.error,
                            bill: { title: displayName }
                        });

                        setTimeout(() => {
                            buttonElement.innerHTML = originalText;
                            buttonElement.style.background = '#27ae60';
                            buttonElement.disabled = false;
                        }, 3000);
                    }
                } catch (error) {
                    buttonElement.innerHTML = '❌ Error';
                    buttonElement.style.background = '#e74c3c';

                    addSummaryResult({
                        success: false,
                        error: error.message,
                        bill: { title: displayName }
                    });

                    setTimeout(() => {
                        buttonElement.innerHTML = originalText;
                        buttonElement.style.background = '#27ae60';
                        buttonElement.disabled = false;
                    }, 3000);
                }
            }

            function addSummaryResult(data) {
                const resultsDiv = document.getElementById('ai-summary-results');
                const timestamp = new Date().toLocaleTimeString();

                let resultHtml = `
                    <div style="border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; background: ${data.success ? '#f8f9fa' : '#fdf2f2'};">
                        <div style="display: flex; justify-content: between; align-items: start; margin-bottom: 10px;">
                            <h4 style="margin: 0; color: ${data.success ? '#2c3e50' : '#e74c3c'};">
                                ${data.success ? '✅' : '❌'} ${data.bill.title}
                            </h4>
                            <small style="color: #666;">${timestamp}</small>
                        </div>
                `;

                if (data.success) {
                    resultHtml += `
                        <div style="margin: 10px 0;">
                            <strong>Bill:</strong> ${data.bill.bill_type}${data.bill.bill_number} (${data.bill.congress}th Congress)<br>
                            <strong>Sponsor:</strong> ${data.bill.sponsor}<br>
                            <strong>Introduced:</strong> ${data.bill.introduced_date}<br>
                            <strong>Status:</strong> ${data.bill.latest_action.substring(0, 100)}...
                        </div>

                        <div style="background: #e8f5e8; padding: 10px; border-radius: 4px; margin: 10px 0;">
                            <strong>🤖 AI Summary (${data.ai_method}):</strong><br>
                            <div style="margin-top: 8px; line-height: 1.5;">
                                ${data.ai_summary}
                            </div>
                        </div>

                        <div style="font-size: 12px; color: #666;">
                            Processing time: ${data.processing_time}s
                        </div>
                    `;

                    // Add full AI result if available (OpenAI)
                    if (data.full_ai_result) {
                        resultHtml += `
                            <details style="margin-top: 10px;">
                                <summary style="cursor: pointer; color: #3498db;">📊 View Full AI Analysis</summary>
                                <div style="background: #f8f9fa; padding: 10px; margin-top: 5px; border-radius: 4px; font-size: 12px;">
                                    <pre style="white-space: pre-wrap; margin: 0;">${JSON.stringify(data.full_ai_result, null, 2)}</pre>
                                </div>
                            </details>
                        `;
                    }
                } else {
                    resultHtml += `
                        <div style="color: #e74c3c;">
                            <strong>Error:</strong> ${data.error}
                        </div>
                    `;
                }

                resultHtml += '</div>';

                // Add to top of results
                if (resultsDiv.innerHTML.includes('AI summaries will appear here')) {
                    resultsDiv.innerHTML = resultHtml;
                } else {
                    resultsDiv.innerHTML = resultHtml + resultsDiv.innerHTML;
                }

                // Scroll to show the new result
                resultsDiv.scrollTop = 0;
            }

            function clearSummaryResults() {
                const resultsDiv = document.getElementById('ai-summary-results');
                resultsDiv.innerHTML = '<p style="color: #666; font-style: italic;">AI summaries will appear here when you click "Summarize" on bills above.</p>';
            }
            
            async function testCompletePipeline() {
                const congress = document.getElementById('congress').value;
                const billType = document.getElementById('bill-type').value;
                const billNumber = document.getElementById('bill-number').value;
                const resultDiv = document.getElementById('complete-pipeline-result');

                resultDiv.innerHTML = '<div class="loading">🔄 Running complete pipeline... This may take 30-60 seconds...</div>';

                try {
                    const response = await fetch(`/api/v1/admin/process-and-save-bill?congress=${congress}&bill_type=${billType}&bill_number=${billNumber}`, {
                        method: 'POST'
                    });
                    const data = await response.json();

                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="success">
                                <h3>✅ Complete Pipeline Success!</h3>
                                <p><strong>Message:</strong> ${data.message}</p>
                                <p><strong>Processing Time:</strong> ${data.processing_time} seconds</p>

                                <h4>📊 Pipeline Steps:</h4>
                                <ul>
                                    <li>✅ Metadata Fetched: ${data.steps_completed.metadata_fetched}</li>
                                    <li>✅ Full Text Fetched: ${data.steps_completed.full_text_fetched}</li>
                                    <li>✅ AI Analysis Generated: ${data.steps_completed.ai_analysis_generated}</li>
                                    <li>✅ Ready for Database: ${data.steps_completed.ready_for_database}</li>
                                </ul>

                                <h4>📋 Bill Information:</h4>
                                <ul>
                                    <li><strong>Title:</strong> ${data.bill_data.title}</li>
                                    <li><strong>Bill Number:</strong> ${data.bill_data.bill_number}</li>
                                    <li><strong>Bill ID:</strong> ${data.bill_id}</li>
                                    <li><strong>Full Text Length:</strong> ${data.bill_data.full_text_length.toLocaleString()} characters</li>
                                    <li><strong>AI Summary:</strong> ${data.bill_data.ai_summary}</li>
                                    <li><strong>Official URL:</strong> <a href="${data.bill_data.official_bill_url}" target="_blank">${data.bill_data.official_bill_url}</a></li>
                                    <li><strong>AI Processed:</strong> ${data.bill_data.ai_processed_at}</li>
                                </ul>

                                <h4>🤖 AI Analysis Results:</h4>
                                <ul>
                                    <li><strong>Support Reasons:</strong> ${data.bill_data.support_reasons ? data.bill_data.support_reasons.join(', ') : 'None'}</li>
                                    <li><strong>Oppose Reasons:</strong> ${data.bill_data.oppose_reasons ? data.bill_data.oppose_reasons.join(', ') : 'None'}</li>
                                    <li><strong>AI Tags:</strong> ${data.bill_data.ai_tags ? data.bill_data.ai_tags.join(', ') : 'None'}</li>
                                </ul>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ Pipeline Failed: ${data.error}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                }
            }

            async function loadDatabaseRecords() {
                const resultDiv = document.getElementById('database-records');
                resultDiv.innerHTML = '<div class="loading">🔄 Loading database records...</div>';

                try {
                    const response = await fetch('/api/v1/admin/database-bills');
                    const data = await response.json();

                    if (data.success) {
                        let html = `
                            <div class="success">
                                <h3>📊 Database Records (${data.count} bills)</h3>
                                <div style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; margin-top: 10px;">
                        `;

                        data.bills.forEach(bill => {
                            html += `
                                <div style="border-bottom: 1px solid #eee; padding: 10px 0; margin-bottom: 10px;">
                                    <h4>${bill.title}</h4>
                                    <p><strong>Bill Number:</strong> ${bill.bill_number}</p>
                                    <p><strong>ID:</strong> ${bill.id}</p>
                                    <p><strong>AI Summary:</strong> ${bill.ai_summary || 'Not processed'}</p>
                                    <p><strong>Full Text Length:</strong> ${bill.full_text_length.toLocaleString()} characters</p>
                                    <p><strong>Official URL:</strong> ${bill.official_bill_url || 'Not set'}</p>
                                    <p><strong>Support Reasons:</strong> ${bill.support_reasons ? JSON.stringify(bill.support_reasons) : 'None'}</p>
                                    <p><strong>Oppose Reasons:</strong> ${bill.oppose_reasons ? JSON.stringify(bill.oppose_reasons) : 'None'}</p>
                                    <p><strong>AI Tags:</strong> ${bill.ai_tags ? JSON.stringify(bill.ai_tags) : 'None'}</p>
                                    <p><strong>Created:</strong> ${bill.created_at}</p>
                                    <p><strong>AI Processed:</strong> ${bill.ai_processed_at || 'Not processed'}</p>
                                </div>
                            `;
                        });

                        html += '</div></div>';
                        resultDiv.innerHTML = html;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ Failed to load records: ${data.error}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                }
            }

            // Load system status on page load
            window.onload = checkSystemStatus;
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@router.post("/test-action-network")
async def test_action_network():
    """
    Test Action Network integration with mock data
    """
    try:
        from app.services.action_network_service import ActionNetworkService

        action_network = ActionNetworkService()

        if not action_network.enabled:
            return {
                "success": False,
                "error": "Action Network service is not enabled (missing API key)"
            }

        # Test with mock data for both chambers
        test_senate_data = {
            'chamber': 'senate',
            'bill_number': 'S.123',
            'position': 'support'
        }

        test_house_data = {
            'chamber': 'house',
            'bill_number': 'H.R.456',
            'position': 'oppose'
        }

        senate_result = await action_network.get_campaign_embed_info(test_senate_data)
        house_result = await action_network.get_campaign_embed_info(test_house_data)
        health_check = action_network.health_check()

        return {
            "success": True,
            "action_network_enabled": action_network.enabled,
            "health_check": health_check,
            "senate_campaign": senate_result,
            "house_campaign": house_result,
            "message": "Action Network embed integration ready. Users can complete forms to send messages to officials."
        }

    except Exception as e:
        logger.error(f"Error testing Action Network: {e}")
        return {
            "success": False,
            "error": f"Action Network test failed: {str(e)}"
        }

@router.post("/test-message-personalization")
async def test_message_personalization():
    """
    Test message personalization service with mock data
    """
    try:
        from app.services.message_personalization_service import MessagePersonalizationService
        from app.services.bills import BillService
        from app.db.database import get_db

        # Get a bill from the database for testing
        db = next(get_db())
        bill_service = BillService(db)
        bills = bill_service.get_bills(limit=1)

        if not bills:
            return {
                "success": False,
                "error": "No bills found in database. Please process a bill first."
            }

        bill = bills[0]

        # Test message personalization
        personalization_service = MessagePersonalizationService()

        test_request_data = {
            'bill': bill,
            'position': 'support',
            'user_info': {
                'first_name': 'Test',
                'last_name': 'User',
                'email': '<EMAIL>',
                'zip_code': '60302'
            },
            'representatives': [
                {
                    'full_name': 'Test Representative',
                    'title': 'Representative',
                    'chamber': 'house',
                    'state': 'IL',
                    'party': 'Democratic',
                    'email': '<EMAIL>',
                    'last_name': 'Representative'
                },
                {
                    'full_name': 'Test Senator',
                    'title': 'Senator',
                    'chamber': 'senate',
                    'state': 'IL',
                    'party': 'Democratic',
                    'email': '<EMAIL>',
                    'last_name': 'Senator'
                }
            ],
            'custom_message': 'This is important to me because it affects my community.',
            'selected_reasons': ['It will improve public services', 'It supports economic growth']
        }

        logger.info(f"Testing message personalization with bill: {bill.bill_number}")

        result = await personalization_service.create_personalized_messages(test_request_data)

        return {
            "success": True,
            "bill_title": bill.title,
            "bill_number": bill.bill_number,
            "personalization_result": result
        }

    except Exception as e:
        logger.error(f"Error testing message personalization: {e}")
        return {
            "success": False,
            "error": f"Message personalization test failed: {str(e)}"
        }

@router.post("/test-complete-action-flow")
async def test_complete_action_flow(db: Session = Depends(get_db)):
    """Test the complete action submission flow without authentication"""
    try:
        # Test data
        test_data = {
            "bill_id": "25177ae6-9916-456b-aec2-7d3b84f87647",
            "stance": "support",
            "selected_reasons": ["This bill addresses an important issue", "The provisions make sense for our community"],
            "custom_message": "This is important to me because it affects my community.",
            "zip_code": "60302",
            "address": "123 Main St",
            "city": "Oak Park",
            "state": "IL"
        }

        # Mock user data
        mock_user = {
            "sub": "test-user-123",
            "email": "<EMAIL>",
            "name": "Test User",
            "given_name": "Test",
            "family_name": "User"
        }

        logger.info(f"Testing complete action flow with bill ID: {test_data['bill_id']}")

        # Step 1: Get bill data
        from app.services.bills import BillService
        bill_service = BillService(db)
        bill = bill_service.get_bill(test_data["bill_id"])

        if not bill:
            return {"success": False, "error": "Bill not found"}

        logger.info(f"Found bill: {bill.bill_number} - {bill.title}")

        # Step 2: Look up representatives
        from app.services.officials_service import OfficialsService
        officials_service = OfficialsService()
        officials_result = await officials_service.lookup_representatives_by_zip(test_data["zip_code"])

        if officials_result["status"] != "success":
            return {"success": False, "error": f"Officials lookup failed: {officials_result.get('message')}"}

        # Combine senators and representative
        representatives = officials_result["senators"] + ([officials_result["representative"]] if officials_result["representative"] else [])
        logger.info(f"Found {len(representatives)} representatives")

        # Step 3: Generate personalized messages
        from app.services.message_personalization_service import MessagePersonalizationService
        personalization_service = MessagePersonalizationService()

        user_info = {
            "first_name": mock_user["given_name"],
            "last_name": mock_user["family_name"],
            "email": mock_user["email"],
            "zip_code": test_data["zip_code"],
            "address": test_data.get("address", ""),
            "city": test_data.get("city", ""),
            "state": test_data.get("state", "")
        }

        personalization_request = {
            "bill": bill,
            "position": test_data["stance"],
            "user_info": user_info,
            "representatives": representatives,
            "custom_message": test_data["custom_message"],
            "selected_reasons": test_data["selected_reasons"]
        }

        personalization_result = await personalization_service.create_personalized_messages(personalization_request)

        if personalization_result["status"] != "success":
            return {"success": False, "error": f"Message personalization failed: {personalization_result.get('message')}"}

        logger.info(f"Generated {personalization_result['total_messages']} personalized messages")

        # Step 4: Test Action Network submission (mock for now)
        from app.services.action_network_service import ActionNetworkService
        action_network_service = ActionNetworkService()

        # Test Action Network submission with mock data
        action_network_results = []
        for message in personalization_result["messages"]:
            # In production, this would call action_network_service.submit_message()
            # For testing, we'll simulate a successful submission
            mock_result = {
                "status": "success",
                "message_id": f"mock-{message['representative']['full_name'].replace(' ', '-').lower()}",
                "representative": message['representative']['full_name'],
                "subject": message['subject'][:50] + "..." if len(message['subject']) > 50 else message['subject']
            }
            action_network_results.append(mock_result)

        return {
            "success": True,
            "bill": {
                "id": bill.id,
                "number": bill.bill_number,
                "title": bill.title
            },
            "officials_lookup": {
                "status": officials_result["status"],
                "total_representatives": len(representatives),
                "representatives": [rep["full_name"] for rep in representatives]
            },
            "message_personalization": {
                "status": personalization_result["status"],
                "total_messages": personalization_result["total_messages"],
                "sample_subject": personalization_result["messages"][0]["subject"] if personalization_result["messages"] else None
            },
            "action_network": {
                "status": "success",
                "total_submissions": len(action_network_results),
                "submissions": action_network_results,
                "note": "Mock submissions completed successfully"
            },
            "test_data": test_data
        }

    except Exception as e:
        logger.error(f"Error testing complete action flow: {e}")
        return {
            "success": False,
            "error": str(e)
        }
