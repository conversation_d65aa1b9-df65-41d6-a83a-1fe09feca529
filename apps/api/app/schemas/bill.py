# app/schemas/bill.py
from pydantic import BaseModel, ConfigDict, HttpUrl, field_serializer
from typing import Optional, List, Dict, Any
from datetime import datetime
from app.models.bill import BillStatus, BillType

class BillBase(BaseModel):
    """Base bill schema with common fields"""
    title: str
    description: Optional[str] = None
    bill_number: str
    bill_type: BillType
    status: BillStatus
    session_year: int
    chamber: str
    state: str = "federal"
    summary: Optional[str] = None
    ai_summary: Optional[str] = None
    simple_summary: Optional[str] = None  # Simple, 8th grade reading level summary

    # Structured AI summary fields (Implementation-2.MD approach)
    summary_what_does: Optional[Dict[str, Any]] = None
    summary_who_affects: Optional[Dict[str, Any]] = None
    summary_why_matters: Optional[Dict[str, Any]] = None
    summary_key_provisions: Optional[Dict[str, Any]] = None
    summary_timeline: Optional[Dict[str, Any]] = None
    summary_cost_impact: Optional[Dict[str, Any]] = None
    source_url: Optional[HttpUrl] = None
    text_url: Optional[HttpUrl] = None
    sponsor_name: Optional[str] = None
    sponsor_party: Optional[str] = None
    sponsor_state: Optional[str] = None
    is_featured: bool = False
    priority_score: int = 0

    @field_serializer('source_url', 'text_url')
    def serialize_urls(self, value):
        return str(value) if value else None

class BillCreate(BillBase):
    """Schema for creating a new bill"""
    full_text: Optional[str] = None
    tags: Optional[List[str]] = None
    categories: Optional[List[str]] = None
    openstates_id: Optional[str] = None
    congress_gov_id: Optional[str] = None

    # AI-generated analysis fields (Sprint A requirements)
    reasons_for_support: Optional[str] = None  # JSON string of arguments
    reasons_for_opposition: Optional[str] = None  # JSON string of arguments
    introduced_date: Optional[datetime] = None
    last_action_date: Optional[datetime] = None
    cosponsors: Optional[str] = None  # JSON string
    vote_history: Optional[str] = None  # JSON string
    bill_metadata: Optional[str] = None  # JSON string

class BillUpdate(BaseModel):
    """Schema for updating bill information"""
    title: Optional[str] = None
    description: Optional[str] = None
    status: Optional[BillStatus] = None
    summary: Optional[str] = None
    ai_summary: Optional[str] = None
    full_text: Optional[str] = None
    source_url: Optional[HttpUrl] = None
    text_url: Optional[HttpUrl] = None
    is_featured: Optional[bool] = None
    priority_score: Optional[int] = None
    tags: Optional[List[str]] = None
    categories: Optional[List[str]] = None

    @field_serializer('source_url', 'text_url')
    def serialize_urls(self, value):
        return str(value) if value else None

class BillResponse(BillBase):
    """Schema for bill API responses"""
    id: str
    created_at: datetime
    updated_at: datetime
    introduced_date: Optional[datetime] = None
    last_action_date: Optional[datetime] = None
    openstates_id: Optional[str] = None
    congress_gov_id: Optional[str] = None
    cosponsors: Optional[List[Dict[str, Any]]] = None
    vote_history: Optional[List[Dict[str, Any]]] = None
    tags: Optional[List[str]] = None
    categories: Optional[List[str]] = None

    # AI-generated analysis fields (Sprint A requirements)
    reasons_for_support: Optional[List[str]] = None  # Parsed arguments list
    reasons_for_opposition: Optional[List[str]] = None  # Parsed arguments list

    # Enhanced AI processing fields (Implementation-2.MD requirements)
    support_reasons: Optional[List[str]] = None  # JSONB array of support reasons
    oppose_reasons: Optional[List[str]] = None  # JSONB array of oppose reasons
    amend_reasons: Optional[List[str]] = None  # JSONB array of amend reasons
    message_templates: Optional[Dict[str, Any]] = None  # JSONB object with message templates
    ai_tags: Optional[List[str]] = None  # JSONB array of AI-generated tags
    ai_processed_at: Optional[datetime] = None  # When AI processing was completed

    model_config = ConfigDict(from_attributes=True)

class Bill(BillResponse):
    """Complete bill schema for internal use"""
    full_text: Optional[str] = None
    bill_metadata: Optional[Dict[str, Any]] = None

class BillSummary(BaseModel):
    """Lightweight bill summary for lists"""
    id: str
    title: str
    bill_number: str
    status: BillStatus
    chamber: str
    ai_summary: Optional[str] = None
    is_featured: bool
    priority_score: int
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

class BillSearch(BaseModel):
    """Schema for bill search parameters"""
    query: Optional[str] = None
    status: Optional[BillStatus] = None
    chamber: Optional[str] = None
    state: Optional[str] = None
    session_year: Optional[int] = None
    tags: Optional[List[str]] = None
    categories: Optional[List[str]] = None
    is_featured: Optional[bool] = None
    limit: int = 20
    offset: int = 0

class BillStats(BaseModel):
    """Bill statistics schema"""
    total_bills: int
    by_status: Dict[str, int]
    by_chamber: Dict[str, int]
    featured_count: int
    recent_updates: int
