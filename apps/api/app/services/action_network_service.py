# app/services/action_network_service.py
"""
Action Network embedded forms service for professional message delivery to representatives.

This service integrates with Action Network's embedded forms to submit personalized messages
to federal representatives on behalf of users. Uses UI-created campaigns that actually
send messages to representatives (unlike API-created campaigns).
"""

import logging
import os
from typing import Dict, Any
from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class ActionNetworkService:
    """Service for integrating with Action Network UI-created campaigns for actual message delivery"""

    def __init__(self):
        self.api_key = getattr(settings, 'ACTION_NETWORK_API_KEY', None) or os.getenv('ACTION_NETWORK_API_KEY')
        self.base_url = "https://actionnetwork.org"

        # UI-created campaign IDs for actual message delivery
        # These must be created manually through Action Network's UI to actually send messages
        self.senate_campaign_id = os.getenv('ACTION_NETWORK_SENATE_CAMPAIGN_ID', 'modernaction-senate-2024')
        self.house_campaign_id = os.getenv('ACTION_NETWORK_HOUSE_CAMPAIGN_ID', 'modernaction-house-2024')

        if not self.api_key:
            logger.warning("ACTION_NETWORK_API_KEY not configured. Action Network features will be disabled.")
            self.enabled = False
        else:
            self.enabled = True
            logger.info(f"Action Network service initialized with Senate: {self.senate_campaign_id}, House: {self.house_campaign_id}")
            logger.info("Using UI-created campaigns for actual message delivery to officials")
    
    async def get_campaign_embed_info(self, message_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get Action Network campaign embed information for frontend integration

        Args:
            message_data: Dict containing:
                - chamber: 'house' or 'senate' to determine which campaign to use
                - bill_number: Bill being addressed
                - position: 'support', 'oppose', or 'amend'

        Returns:
            Dict with campaign embed information for frontend
        """
        if not self.enabled:
            logger.warning("Action Network service is not enabled")
            return {
                'status': 'disabled',
                'message': 'Action Network service is not configured'
            }

        try:
            # Determine which campaign to use based on chamber
            chamber = message_data.get('chamber', 'house').lower()
            campaign_id = self.house_campaign_id if chamber == 'house' else self.senate_campaign_id

            logger.info(f"Providing embed info for {chamber} campaign: {campaign_id}")

            # Return embed information for frontend integration
            return {
                'status': 'success',
                'campaign_id': campaign_id,
                'chamber': chamber,
                'embed_url': f"{self.base_url}/letters/{campaign_id}",
                'iframe_url': f"{self.base_url}/letters/{campaign_id}/embed",
                'message': f"Action Network {chamber} campaign ready for embedding"
            }

        except Exception as e:
            logger.error(f"Failed to get Action Network campaign info: {e}")
            return {
                'status': 'error',
                'message': 'Failed to get campaign information',
                'error': str(e)
            }

    async def submit_message(self, message_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Legacy method - now returns embed information instead of direct submission
        For actual message delivery, users must complete the Action Network form
        """
        logger.info("submit_message called - returning embed information for frontend integration")
        return await self.get_campaign_embed_info(message_data)
    
    async def track_delivery_attempt(self, campaign_id: str, person_data: Dict[str, Any],
                                    bill_number: str = None, position: str = None) -> Dict[str, Any]:
        """
        Track a delivery attempt for analytics (called when user completes Action Network form)
        This method can be called via webhook or frontend callback when user completes the form
        """
        try:
            logger.info(f"Tracking delivery attempt for campaign {campaign_id}")

            # In a real implementation, this would:
            # 1. Update database delivery tracking
            # 2. Send analytics events
            # 3. Update user action history

            return {
                'success': True,
                'campaign_id': campaign_id,
                'message': 'Delivery attempt tracked successfully'
            }

        except Exception as e:
            logger.error(f"Error tracking delivery attempt: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    

    
    def health_check(self) -> Dict[str, Any]:
        """Check the health of the Action Network service configuration"""
        if not self.enabled:
            return {
                'status': 'disabled',
                'message': 'Action Network API key not configured'
            }

        try:
            # Check that campaign IDs are configured
            if not self.senate_campaign_id or not self.house_campaign_id:
                return {
                    'status': 'error',
                    'message': 'Action Network campaign IDs not configured'
                }

            return {
                'status': 'healthy',
                'message': f'Action Network service ready (Senate: {self.senate_campaign_id}, House: {self.house_campaign_id})',
                'senate_url': f"{self.base_url}/letters/{self.senate_campaign_id}",
                'house_url': f"{self.base_url}/letters/{self.house_campaign_id}"
            }

        except Exception as e:
            return {
                'status': 'error',
                'message': f'Action Network service error: {e}'
            }


# Convenience function for easy import
def get_action_network_service() -> ActionNetworkService:
    """Get an Action Network service instance"""
    return ActionNetworkService()
