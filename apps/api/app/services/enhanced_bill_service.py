# app/services/enhanced_bill_service.py
"""
Enhanced Bill Data Service - Implementation-2.MD compliant service.

This service provides the complete bill processing pipeline using:
- Congress.gov API for bill data
- OpenAI GPT-4 for comprehensive AI analysis
- Async processing for performance
- JSONB storage for structured data
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.db.database import get_async_db, AsyncSessionLocal
from app.models.bill import Bill, BillStatus, BillType
from app.services.congress_gov_api import CongressGovAPI
from app.services.ai_service import get_ai_service

logger = logging.getLogger(__name__)


class EnhancedBillService:
    """Enhanced service for complete bill data ingestion and AI processing"""
    
    def __init__(self):
        self.congress_api = CongressGovAPI()
        self.ai_service = get_ai_service()
        
    async def process_bill(self, bill_number: str, session: str = "118") -> Dict[str, Any]:
        """
        Complete bill processing pipeline
        
        Args:
            bill_number: e.g., "HR5", "S1234"
            session: Congress session (default: "118")
            
        Returns:
            Dict with processing results and bill data
        """
        try:
            logger.info(f"Starting complete processing for bill {bill_number} from session {session}")
            
            # Step 1: Check if bill already exists and is processed
            async for session_db in get_async_db():
                existing_bill = await self._get_existing_bill(session_db, bill_number, session)
                if existing_bill and existing_bill.ai_processed_at:
                    logger.info(f"Bill {bill_number} already processed, skipping")
                    return {
                        'status': 'already_processed',
                        'bill_id': existing_bill.id,
                        'message': f'Bill {bill_number} was already processed'
                    }
                break
            
            # Step 2: Fetch bill data from Congress.gov
            logger.info(f"Fetching bill data for {bill_number}")
            # Parse bill number to get components
            bill_parts = self.congress_api.parse_bill_number(bill_number)
            bill_metadata = self.congress_api.get_bill_by_number(
                int(session), bill_parts['bill_type'], bill_parts['number']
            )

            if not bill_metadata:
                raise Exception(f"Bill {bill_number} not found in Congress {session}")

            # Get full text
            full_text = await self.congress_api.get_bill_full_text(
                int(session), bill_parts['bill_type'], bill_parts['number']
            )

            # Combine metadata and full text
            bill_data = {
                'number': bill_number,
                'title': bill_metadata.get('title', ''),
                'summary': bill_metadata.get('summary', ''),
                'sponsors': bill_metadata.get('sponsors', []),
                'introduced_date': bill_metadata.get('introducedDate'),
                'latest_action': bill_metadata.get('latestAction', {}),
                'full_text': full_text or '',
                'status': bill_metadata.get('latestAction', {}).get('text', 'Unknown')
            }
            
            # Step 3: Process with AI
            logger.info(f"Processing bill {bill_number} with AI")
            ai_results = await self.ai_service.process_bill_complete(
                bill_data['full_text'], 
                bill_data
            )
            
            # Step 4: Save to database
            logger.info(f"Saving bill {bill_number} to database")
            bill_id = await self._save_bill_to_database(bill_data, ai_results, session)

            logger.info(f"Successfully processed bill {bill_number} with ID {bill_id}")
            return {
                'status': 'success',
                'bill_id': bill_id,
                'bill_number': bill_number,
                'session': session,
                'title': bill_data.get('title', ''),
                'structured_summary': ai_results['structured_summary'],
                'support_reasons': ai_results['support_reasons'],
                'oppose_reasons': ai_results['oppose_reasons'],
                'tags': ai_results['tags'],
                'full_text_length': len(bill_data.get('full_text', '')),
                'message': f'Successfully processed bill {bill_number}'
            }
            
        except Exception as e:
            logger.error(f"Failed to process bill {bill_number}: {e}")
            return {
                'status': 'error',
                'bill_number': bill_number,
                'session': session,
                'error': str(e),
                'message': f'Failed to process bill {bill_number}: {e}'
            }
    
    async def _get_existing_bill(self, session: AsyncSession, bill_number: str, congress_session: str) -> Optional[Bill]:
        """Check if bill already exists in database"""
        try:
            # Normalize bill number for comparison
            normalized_number = bill_number.upper().replace('.', '')
            
            stmt = select(Bill).where(
                Bill.bill_number.ilike(f"%{normalized_number}%"),
                Bill.session_year == int(congress_session)
            )
            result = await session.execute(stmt)
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.warning(f"Error checking for existing bill: {e}")
            return None
    
    async def _save_bill_to_database(self, bill_data: Dict[str, Any], ai_results: Dict[str, Any], session: str) -> Optional[str]:
        """Save bill and AI results to database"""
        async with AsyncSessionLocal() as session_db:
            try:
                # Check if bill already exists
                existing_bill = await self._get_existing_bill(session_db, bill_data['number'], session)
                
                if existing_bill:
                    # Update existing bill with AI results
                    bill = existing_bill
                    logger.info(f"Updating existing bill {bill.id}")
                else:
                    # Create new bill
                    bill = Bill()
                    logger.info(f"Creating new bill for {bill_data['number']}")
                
                # Set basic bill data
                bill.title = bill_data['title'][:500] if bill_data['title'] else 'Untitled Bill'
                bill.description = bill_data.get('short_title', '')[:1000] if bill_data.get('short_title') else ''
                bill.bill_number = bill_data['number']
                bill.bill_type = self._extract_bill_type(bill_data['number'])
                bill.status = self._map_bill_status(bill_data.get('status', 'Unknown'))
                bill.session_year = int(session)
                bill.chamber = self._determine_chamber(bill_data['number'])
                bill.state = 'federal'  # All Congress.gov bills are federal
                bill.full_text = bill_data.get('full_text', '')
                bill.summary = bill_data.get('summary', '')
                
                # Set AI-generated content
                logger.info(f"Setting AI results: {list(ai_results.keys())}")

                # Set structured summary JSONB fields (Implementation-2.MD approach)
                structured_summary = ai_results.get('structured_summary', {})
                bill.summary_what_does = structured_summary.get('what_does')
                bill.summary_who_affects = structured_summary.get('who_affects')
                bill.summary_why_matters = structured_summary.get('why_matters')
                bill.summary_key_provisions = structured_summary.get('key_provisions')
                bill.summary_timeline = structured_summary.get('timeline')
                bill.summary_cost_impact = structured_summary.get('cost_impact')

                bill.support_reasons = ai_results['support_reasons']
                bill.oppose_reasons = ai_results['oppose_reasons']
                bill.amend_reasons = ai_results['amend_reasons']
                bill.message_templates = ai_results['message_templates']
                bill.ai_tags = ai_results['tags']
                bill.ai_processed_at = datetime.utcnow()
                
                # Legacy fields for backward compatibility
                bill.reasons_for_support = '; '.join(ai_results['support_reasons'][:3])
                bill.reasons_for_opposition = '; '.join(ai_results['oppose_reasons'][:3])
                
                if not existing_bill:
                    session_db.add(bill)
                
                await session_db.commit()
                await session_db.refresh(bill)

                # Capture the bill ID before session closes
                bill_id = bill.id
                logger.info(f"Successfully saved bill {bill_id} to database")
                return bill_id
                
            except Exception as e:
                await session_db.rollback()
                logger.error(f"Database error saving bill: {e}")
                raise
    
    def _extract_bill_type(self, bill_number: str) -> str:
        """Extract bill type from bill number (HR, S, etc.)"""
        if not bill_number:
            return 'unknown'
        
        # Remove dots and get first part
        clean_number = bill_number.upper().replace('.', '')
        if clean_number.startswith('HR'):
            return 'house_bill'
        elif clean_number.startswith('S') and not clean_number.startswith('SJ'):
            return 'senate_bill'
        elif clean_number.startswith('HJ'):
            return 'house_joint_resolution'
        elif clean_number.startswith('SJ'):
            return 'senate_joint_resolution'
        elif clean_number.startswith('HC'):
            return 'house_concurrent_resolution'
        elif clean_number.startswith('SC'):
            return 'senate_concurrent_resolution'
        else:
            return 'other'
    
    def _determine_chamber(self, bill_number: str) -> str:
        """Determine originating chamber from bill number"""
        if not bill_number:
            return 'unknown'
            
        clean_number = bill_number.upper().replace('.', '')
        if clean_number.startswith('H'):
            return 'house'
        elif clean_number.startswith('S'):
            return 'senate'
        else:
            return 'unknown'

    def _map_bill_status(self, status_text: str) -> BillStatus:
        """Map Congress.gov status text to our BillStatus enum"""
        status_lower = status_text.lower()

        if 'introduced' in status_lower:
            return BillStatus.INTRODUCED
        elif 'committee' in status_lower:
            return BillStatus.COMMITTEE
        elif 'passed' in status_lower:
            return BillStatus.PASSED
        elif 'signed' in status_lower:
            return BillStatus.SIGNED
        elif 'vetoed' in status_lower:
            return BillStatus.VETOED
        elif 'failed' in status_lower:
            return BillStatus.FAILED
        else:
            return BillStatus.INTRODUCED  # Default for unknown status

    async def get_bill_status(self, bill_id: int) -> Dict[str, Any]:
        """Get processing status of a bill"""
        async for session in get_async_db():
            try:
                stmt = select(Bill).where(Bill.id == bill_id)
                result = await session.execute(stmt)
                bill = result.scalar_one_or_none()
                
                if not bill:
                    return {'status': 'not_found', 'message': 'Bill not found'}
                
                return {
                    'status': 'found',
                    'bill_id': bill.id,
                    'bill_number': bill.bill_number,
                    'title': bill.title,
                    'ai_processed': bill.ai_processed_at is not None,
                    'ai_processed_at': bill.ai_processed_at.isoformat() if bill.ai_processed_at else None,
                    'has_structured_summary': bool(bill.summary_what_does),
                    'has_support_reasons': bool(bill.support_reasons),
                    'has_oppose_reasons': bool(bill.oppose_reasons),
                    'has_message_templates': bool(bill.message_templates),
                    'tags': bill.ai_tags or []
                }
                
            except Exception as e:
                logger.error(f"Error getting bill status: {e}")
                return {'status': 'error', 'message': str(e)}
            finally:
                await session.close()
                break


# Convenience function for easy import
def get_enhanced_bill_service() -> EnhancedBillService:
    """Get an enhanced bill service instance"""
    return EnhancedBillService()
