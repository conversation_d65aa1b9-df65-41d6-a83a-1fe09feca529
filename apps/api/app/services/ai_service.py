# app/services/ai_service.py
"""
Enhanced AI service for bill processing using OpenAI GPT-4.

This service provides comprehensive AI analysis of bills including:
- Plain English summaries
- Support/oppose/amend reasons
- Message templates
- Categorization tags
"""

import openai
import asyncio
import json
import logging
import os
import asyncio
from typing import Dict, List
from tenacity import retry, stop_after_attempt, wait_exponential
from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class AIService:
    """Enhanced AI service for comprehensive bill analysis using OpenAI GPT-4"""
    
    def __init__(self):
        self.api_key = getattr(settings, 'OPENAI_API_KEY', None) or os.getenv('OPENAI_API_KEY')
        
        if not self.api_key:
            logger.warning("OPENAI_API_KEY not configured. AI processing features will be disabled.")
            self.enabled = False
        else:
            self.client = openai.AsyncOpenAI(api_key=self.api_key)
            self.enabled = True
            logger.info("OpenAI AI service initialized successfully")

    async def generate_simple_summary(self, bill_text: str, metadata: dict) -> str:
        """Generate a simple, 8th grade reading level summary for bill cards"""
        prompt = f"""
        Create a simple, easy-to-understand summary of this bill for the general public.

        BILL: {metadata['title']}
        TEXT: {bill_text[:2000]}...

        Requirements:
        - Write at an 8th grade reading level
        - Use simple, everyday words
        - Keep it engaging and relatable
        - 2-3 sentences maximum
        - Focus on what this means for regular people
        - Avoid jargon, technical terms, or complex language

        Example style: "This bill would make prescription drugs cheaper for seniors by allowing Medicare to negotiate prices directly with drug companies. It could save families hundreds of dollars per year on medications."

        Write ONLY the summary, nothing else:
        """

        try:
            if not self.enabled:
                # Fallback for when AI is not available
                return f"This bill, {metadata['title']}, is currently being considered by Congress."

            response = await self._make_openai_request(
                messages=[
                    {"role": "system", "content": "You are an expert at explaining complex legislation in simple, clear language that anyone can understand."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=150,
                temperature=0.3
            )

            summary = response.choices[0].message.content.strip()
            logger.info(f"Generated simple summary: {len(summary)} characters")
            return summary

        except Exception as e:
            logger.error(f"Failed to generate simple summary: {e}")
            # Fallback to a basic summary
            return f"This bill, {metadata['title']}, is currently being considered by Congress."

    async def process_bill_complete(self, bill_text: str, bill_metadata: dict) -> dict:
        """
        Generate all AI content for a bill
        
        Returns:
            {
                'ai_summary': str,
                'support_reasons': List[str],
                'oppose_reasons': List[str], 
                'amend_reasons': List[str],
                'message_templates': dict,
                'tags': List[str]
            }
        """
        if not self.enabled:
            logger.warning("AI service is not enabled. Returning fallback data.")
            return self._get_fallback_ai_data(bill_metadata)
            
        # Truncate text if too long for AI model (OpenAI has token limits)
        if len(bill_text) > 50000:  # Reduced limit to avoid rate limiting
            # Keep the beginning (title, summary) and a substantial portion of the bill
            bill_text = bill_text[:50000] + "\n\n... [text truncated for processing - analysis based on first 50,000 characters]"
            logger.info(f"Truncated bill text to 50,000 characters to avoid rate limits")
        
        # Sequential processing with delays to avoid rate limits
        logger.info("Starting complete AI analysis with rate limiting...")
        logger.info(f"Processing bill: {bill_metadata.get('title', 'Unknown')}")
        logger.info(f"Bill text length: {len(bill_text)} characters")

        try:
            # Step 1: Generate structured summary
            logger.info("Step 1: Generating structured AI summary...")
            structured_summary = await self._generate_summary(bill_text, bill_metadata)
            logger.info(f"Structured summary generated with {len(structured_summary)} sections")

            # Small delay to avoid rate limits
            await asyncio.sleep(1)

            # Step 2: Generate support reasons
            logger.info("Step 2: Generating support reasons...")
            support_reasons = await self._generate_support_reasons(bill_text, bill_metadata)
            logger.info(f"Support reasons generated: {len(support_reasons)} reasons")

            # Small delay to avoid rate limits
            await asyncio.sleep(1)

            # Step 3: Generate oppose reasons
            logger.info("Step 3: Generating oppose reasons...")
            oppose_reasons = await self._generate_oppose_reasons(bill_text, bill_metadata)
            logger.info(f"Oppose reasons generated: {len(oppose_reasons)} reasons")

            # Small delay to avoid rate limits
            await asyncio.sleep(1)

            # Step 4: Generate amend reasons
            logger.info("Step 4: Generating amend reasons...")
            amend_reasons = await self._generate_amend_reasons(bill_text, bill_metadata)
            logger.info(f"Amend reasons generated: {len(amend_reasons)} reasons")

            # Small delay to avoid rate limits
            await asyncio.sleep(1)

            # Step 5: Generate message templates
            logger.info("Step 5: Generating message templates...")
            message_templates = await self._generate_message_templates(bill_text, bill_metadata)
            logger.info(f"Message templates generated: {list(message_templates.keys())}")

            # Small delay to avoid rate limits
            await asyncio.sleep(1)

            # Step 6: Generate tags
            logger.info("Step 6: Generating AI tags...")
            tags = await self._generate_tags(bill_text, bill_metadata)
            logger.info(f"Tags generated: {len(tags)} tags")

            result = {
                'structured_summary': structured_summary,
                'support_reasons': support_reasons,
                'oppose_reasons': oppose_reasons,
                'amend_reasons': amend_reasons,
                'message_templates': message_templates,
                'tags': tags
            }

            logger.info("Complete AI analysis finished successfully")
            return result

        except Exception as e:
            logger.error(f"AI analysis failed: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return self._get_fallback_ai_data(bill_metadata)

        # Original parallel processing (commented out to avoid rate limits)
        # tasks = [
        #     self._generate_summary(bill_text, bill_metadata),
        #     self._generate_support_reasons(bill_text, bill_metadata),
        #     self._generate_oppose_reasons(bill_text, bill_metadata),
        #     self._generate_amend_reasons(bill_text, bill_metadata),
        #     self._generate_message_templates(bill_text, bill_metadata),
        #     self._generate_tags(bill_text, bill_metadata)
        # ]
        
        # Original parallel processing (commented out to avoid rate limits)
        # try:
        #     results = await asyncio.gather(*tasks, return_exceptions=True)
        #
        #     # Check if any tasks failed
        #     failed_tasks = [i for i, result in enumerate(results) if isinstance(result, Exception)]
        #     if failed_tasks:
        #         logger.error(f"AI tasks failed: {[results[i] for i in failed_tasks]}")
        #         # For now, return fallback data if any task fails
        #         return self._get_fallback_ai_data(bill_metadata)
        #
        #     return {
        #         'ai_summary': results[0],
        #         'support_reasons': results[1],
        #         'oppose_reasons': results[2],
        #         'amend_reasons': results[3],
        #         'message_templates': results[4],
        #         'tags': results[5]
        #     }
        # except Exception as e:
        #     logger.error(f"AI processing failed: {e}")
        #     import traceback
        #     logger.error(f"Traceback: {traceback.format_exc()}")
        #     # Return minimal fallback data
        #     return self._get_fallback_ai_data(bill_metadata)
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _generate_summary(self, bill_text: str, metadata: dict) -> dict:
        """Generate structured summary as JSON (Implementation-2.MD approach)"""

        # Structured prompt for JSON output as per Implementation-2.MD guide
        prompt = f"""
You are a civic education expert. Analyze this bill and provide a structured summary with specific sections.

BILL TITLE: {metadata.get('title', 'Unknown Bill')}
BILL TEXT: {bill_text}

Generate a structured analysis with exactly these sections. Return ONLY valid JSON:

{{
  "what_does": {{
    "title": "What This Bill Does",
    "content": "2-3 sentence overview in plain English",
    "key_points": ["3-4 specific bullet points about main provisions"]
  }},
  "who_affects": {{
    "title": "Who This Affects",
    "content": "2-3 sentences about who is impacted",
    "affected_groups": ["List of 3-5 specific groups affected"]
  }},
  "why_matters": {{
    "title": "Why It Matters to You",
    "content": "2-3 sentences explaining personal relevance",
    "benefits": ["2-3 potential benefits"],
    "concerns": ["2-3 potential concerns"]
  }},
  "key_provisions": {{
    "title": "Key Provisions",
    "content": "Brief overview of main provisions",
    "provisions": ["4-6 specific provisions or requirements"]
  }},
  "timeline": {{
    "title": "Implementation Timeline",
    "content": "When this takes effect",
    "milestones": ["Key dates and deadlines"]
  }},
  "cost_impact": {{
    "title": "Cost Impact",
    "content": "Financial implications in plain English",
    "estimates": ["Specific cost figures if mentioned"]
  }}
}}

JSON Response:
"""

        response = await self.client.chat.completions.create(
            model="gpt-4-turbo",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=2000,
            temperature=0.3
        )

        content = response.choices[0].message.content.strip()

        try:
            # Extract and parse JSON
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            json_str = content[start_idx:end_idx]

            import json
            structured_summary = json.loads(json_str)

            # Validate required sections
            required_sections = ['what_does', 'who_affects', 'why_matters', 'key_provisions']
            if all(section in structured_summary for section in required_sections):
                return structured_summary
            else:
                raise ValueError("Missing required summary sections")

        except Exception as e:
            logger.error(f"Structured summary generation failed: {e}")
            return self._get_fallback_structured_summary(metadata)

    def _get_fallback_structured_summary(self, metadata: dict) -> dict:
        """Fallback structured summary if AI fails (Implementation-2.MD approach)"""
        return {
            "what_does": {
                "title": "What This Bill Does",
                "content": f"Analysis of {metadata.get('title', 'this bill')} is being processed.",
                "key_points": ["Detailed analysis will be available soon"]
            },
            "who_affects": {
                "title": "Who This Affects",
                "content": "Impact analysis is being processed.",
                "affected_groups": ["Analysis pending"]
            },
            "why_matters": {
                "title": "Why It Matters to You",
                "content": "Personal relevance analysis is being processed.",
                "benefits": ["Analysis pending"],
                "concerns": ["Analysis pending"]
            },
            "key_provisions": {
                "title": "Key Provisions",
                "content": "Provision analysis is being processed.",
                "provisions": ["Analysis pending"]
            },
            "timeline": {
                "title": "Implementation Timeline",
                "content": "Timeline analysis not available",
                "milestones": []
            },
            "cost_impact": {
                "title": "Cost Impact",
                "content": "Cost analysis not available",
                "estimates": []
            }
        }
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _generate_support_reasons(self, bill_text: str, metadata: dict) -> List[str]:
        """Generate selectable reasons for supporting the bill"""
        prompt = f"""
        Generate specific, selectable reasons why someone would SUPPORT this bill.

        BILL: {metadata['title']}
        TEXT: {bill_text}

        Create 6-8 distinct reasons someone might support this bill. Each reason should be:
        - One clear, concise sentence (10-15 words max)
        - Specific to this bill's actual provisions
        - Something a real person would say
        - Focused on benefits/positive outcomes

        Format as a JSON array of strings:
        ["Reason 1", "Reason 2", "Reason 3", ...]

        Support Reasons:
        """
        
        response = await self.client.chat.completions.create(
            model="gpt-4-turbo",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=400,
            temperature=0.3
        )
        
        try:
            content = response.choices[0].message.content.strip()
            # Extract JSON from response
            start_idx = content.find('[')
            end_idx = content.rfind(']') + 1
            json_str = content[start_idx:end_idx]
            reasons = json.loads(json_str)
            
            if isinstance(reasons, list) and all(isinstance(r, str) for r in reasons):
                return reasons[:8]  # Limit to 8 reasons max
            else:
                raise ValueError("Invalid format")
                
        except Exception as e:
            logger.warning(f"Failed to parse support reasons JSON: {e}")
            return [
                "This bill addresses an important issue",
                "The provisions make sense for our community",
                "This legislation is needed to solve current problems"
            ]
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _generate_oppose_reasons(self, bill_text: str, metadata: dict) -> List[str]:
        """Generate selectable reasons for opposing the bill"""
        prompt = f"""
        Generate specific, selectable reasons why someone would OPPOSE this bill.

        BILL: {metadata['title']}
        TEXT: {bill_text}

        Create 6-8 distinct reasons someone might oppose this bill. Each reason should be:
        - One clear, concise sentence (10-15 words max)
        - Specific to this bill's actual provisions
        - Something a real person would say
        - Focused on concerns/negative impacts

        Format as a JSON array of strings:
        ["Reason 1", "Reason 2", "Reason 3", ...]

        Opposition Reasons:
        """
        
        response = await self.client.chat.completions.create(
            model="gpt-4-turbo",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=400,
            temperature=0.3
        )
        
        try:
            content = response.choices[0].message.content.strip()
            start_idx = content.find('[')
            end_idx = content.rfind(']') + 1
            json_str = content[start_idx:end_idx]
            reasons = json.loads(json_str)
            
            if isinstance(reasons, list) and all(isinstance(r, str) for r in reasons):
                return reasons[:8]
            else:
                raise ValueError("Invalid format")
                
        except Exception as e:
            logger.warning(f"Failed to parse oppose reasons JSON: {e}")
            return [
                "This bill may have unintended consequences",
                "The legislation could be too costly to implement",
                "There are concerns about federal overreach"
            ]

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _generate_amend_reasons(self, bill_text: str, metadata: dict) -> List[str]:
        """Generate selectable reasons for amending the bill"""
        prompt = f"""
        Generate specific, selectable reasons why someone would want to AMEND this bill.

        BILL: {metadata['title']}
        TEXT: {bill_text}

        Create 6-8 distinct reasons someone might want to amend this bill. Each reason should be:
        - One clear, concise sentence (10-15 words max)
        - Specific to this bill's actual provisions
        - Something a real person would say
        - Focused on improvements/modifications needed

        Format as a JSON array of strings:
        ["Reason 1", "Reason 2", "Reason 3", ...]

        Amendment Reasons:
        """

        response = await self.client.chat.completions.create(
            model="gpt-4-turbo",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=400,
            temperature=0.3
        )

        try:
            content = response.choices[0].message.content.strip()
            start_idx = content.find('[')
            end_idx = content.rfind(']') + 1
            json_str = content[start_idx:end_idx]
            reasons = json.loads(json_str)

            if isinstance(reasons, list) and all(isinstance(r, str) for r in reasons):
                return reasons[:8]
            else:
                raise ValueError("Invalid format")

        except Exception as e:
            logger.warning(f"Failed to parse amend reasons JSON: {e}")
            return [
                "The bill needs stronger enforcement mechanisms",
                "Some provisions should be clarified or refined",
                "Additional protections should be included"
            ]

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _generate_message_templates(self, bill_text: str, metadata: dict) -> dict:
        """Generate message templates for each position"""
        prompt = f"""
        Create professional message templates for contacting representatives about this bill.

        BILL: {metadata['title']}
        TEXT: {bill_text}

        Generate three message templates:
        1. SUPPORT template - professional message supporting the bill
        2. OPPOSE template - professional message opposing the bill
        3. AMEND template - professional message requesting amendments

        Each template should:
        - Be 2-3 sentences
        - Sound professional but personal
        - Include specific bill details
        - Have placeholders for [REPRESENTATIVE_NAME] and [CONSTITUENT_NAME]

        Format as JSON:
        {{
          "support": "Template text...",
          "oppose": "Template text...",
          "amend": "Template text..."
        }}

        Templates:
        """

        response = await self.client.chat.completions.create(
            model="gpt-4-turbo",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=600,
            temperature=0.3
        )

        try:
            content = response.choices[0].message.content.strip()
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            json_str = content[start_idx:end_idx]
            templates = json.loads(json_str)

            if isinstance(templates, dict) and all(k in templates for k in ['support', 'oppose', 'amend']):
                return templates
            else:
                raise ValueError("Invalid format")

        except Exception as e:
            logger.warning(f"Failed to parse message templates JSON: {e}")
            return {
                "support": "Dear [REPRESENTATIVE_NAME], I am writing to express my support for this important legislation. As your constituent, I believe this bill will benefit our community and urge you to vote in favor. Thank you for your consideration.",
                "oppose": "Dear [REPRESENTATIVE_NAME], I am writing to express my concerns about this proposed legislation. As your constituent, I believe this bill may have negative impacts and urge you to vote against it. Thank you for your consideration.",
                "amend": "Dear [REPRESENTATIVE_NAME], I am writing about this proposed legislation. While I see merit in its goals, I believe amendments are needed to address certain concerns. I urge you to work toward improving this bill. Thank you for your consideration."
            }

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _generate_tags(self, bill_text: str, metadata: dict) -> List[str]:
        """Generate categorization tags for the bill"""
        prompt = f"""
        Generate categorization tags for this bill to help with organization and search.

        BILL: {metadata['title']}
        TEXT: {bill_text}

        Create 5-8 tags that categorize this bill. Tags should be:
        - Single words or short phrases (1-3 words)
        - Relevant to the bill's subject matter
        - Useful for search and filtering
        - Common policy areas (healthcare, education, environment, etc.)

        Format as a JSON array of strings:
        ["tag1", "tag2", "tag3", ...]

        Tags:
        """

        response = await self.client.chat.completions.create(
            model="gpt-4-turbo",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=200,
            temperature=0.3
        )

        try:
            content = response.choices[0].message.content.strip()
            start_idx = content.find('[')
            end_idx = content.rfind(']') + 1
            json_str = content[start_idx:end_idx]
            tags = json.loads(json_str)

            if isinstance(tags, list) and all(isinstance(t, str) for t in tags):
                return tags[:8]
            else:
                raise ValueError("Invalid format")

        except Exception as e:
            logger.warning(f"Failed to parse tags JSON: {e}")
            return ["legislation", "policy", "government"]

    async def _make_openai_request(self, messages: list, max_tokens: int = 800, temperature: float = 0.7) -> str:
        """Generic method for making OpenAI API requests"""
        if not self.enabled:
            raise RuntimeError("AI service is not enabled - OpenAI API key not configured")

        response = await self.client.chat.completions.create(
            model="gpt-4-turbo",
            messages=messages,
            max_tokens=max_tokens,
            temperature=temperature
        )

        return response.choices[0].message.content.strip()

    def _get_fallback_ai_data(self, metadata: dict) -> dict:
        """Return fallback data when AI processing fails"""
        return {
            'ai_summary': f"This bill, {metadata['title']}, is currently being processed. Full AI analysis will be available soon.",
            'support_reasons': [
                "This bill addresses an important issue",
                "The legislation could benefit our community",
                "This represents good policy direction"
            ],
            'oppose_reasons': [
                "This bill may have unintended consequences",
                "The legislation could be too costly",
                "There are concerns about implementation"
            ],
            'amend_reasons': [
                "The bill needs stronger enforcement",
                "Some provisions should be clarified",
                "Additional protections are needed"
            ],
            'message_templates': {
                "support": "Dear [REPRESENTATIVE_NAME], I am writing to express my support for this important legislation. As your constituent, I believe this bill will benefit our community. Thank you for your consideration.",
                "oppose": "Dear [REPRESENTATIVE_NAME], I am writing to express my concerns about this proposed legislation. As your constituent, I have reservations about its potential impacts. Thank you for your consideration.",
                "amend": "Dear [REPRESENTATIVE_NAME], I am writing about this proposed legislation. While I see merit in its goals, I believe amendments would improve it. Thank you for your consideration."
            },
            'tags': ["legislation", "policy", "government"]
        }


# Convenience function for easy import
def get_ai_service() -> AIService:
    """Get an AI service instance"""
    return AIService()
