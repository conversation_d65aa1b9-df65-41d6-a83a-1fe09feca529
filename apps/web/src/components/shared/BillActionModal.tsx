import React, { Fragment, useState, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { useForm } from 'react-hook-form';
import { XMarkIcon, SparklesIcon, CheckIcon } from '@heroicons/react/24/outline';
import { Bill } from '../../types';
import { billActionApi, BillActionData, BillActionSubmitRequest, MessagePreviewRequest } from '../../services/apiClient';
import toast from 'react-hot-toast';

interface BillActionModalProps {
  isOpen: boolean;
  onClose: () => void;
  bill: Bill;
  onSubmit?: (result: any) => void;
}

interface BillActionFormData {
  stance: 'support' | 'oppose' | 'amend';
  selected_reasons: string[];
  custom_reasons: string[];
  custom_message: string;
  zip_code: string;
  email: string;
  address: string;
  city: string;
  state: string;
}

interface MessagePreviewData {
  representatives: any[];
  personalized_messages: any[];
  stance: string;
  selected_reasons: string[];
  custom_reasons: string[];
}

const BillActionModal: React.FC<BillActionModalProps> = ({
  isOpen,
  onClose,
  bill,
  onSubmit
}) => {
  const [billActionData, setBillActionData] = useState<BillActionData | null>(null);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState<'stance' | 'reasons' | 'location' | 'preview' | 'message' | 'action_network'>('stance');
  const [actionNetworkData, setActionNetworkData] = useState<any>(null);
  const [messagePreview, setMessagePreview] = useState<MessagePreviewData | null>(null);
  const [isLoadingPreview, setIsLoadingPreview] = useState(false);
  const [aiProgress, setAiProgress] = useState(0);
  const [aiProgressMessage, setAiProgressMessage] = useState('');
  const [customReasonInput, setCustomReasonInput] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
    getValues
  } = useForm<BillActionFormData>({
    mode: 'onChange',
    defaultValues: {
      stance: 'support',
      selected_reasons: [],
      custom_reasons: [],
      custom_message: '',
      zip_code: '',
      email: '',
      address: '',
      city: '',
      state: ''
    }
  });

  const watchedStance = watch('stance');
  const watchedReasons = watch('selected_reasons');
  const watchedCustomReasons = watch('custom_reasons');
  const watchedZipCode = watch('zip_code');
  const watchedEmail = watch('email');

  // Load bill action data when modal opens
  useEffect(() => {
    if (isOpen && bill?.id) {
      loadBillActionData();
    }
  }, [isOpen, bill?.id]);

  const loadBillActionData = async () => {
    try {
      setIsLoadingData(true);
      const data = await billActionApi.getBillActionData(bill.id);
      setBillActionData(data);
    } catch (error) {
      console.error('Failed to load bill action data:', error);
      toast.error('Failed to load bill information. Please try again.');
    } finally {
      setIsLoadingData(false);
    }
  };

  const handleClose = () => {
    reset();
    setBillActionData(null);
    setCurrentStep('stance');
    onClose();
  };

  const handleStanceSelect = (stance: 'support' | 'oppose' | 'amend') => {
    setValue('stance', stance);
    setValue('selected_reasons', []); // Reset reasons when stance changes
    setCurrentStep('reasons');
  };

  const handleReasonToggle = (reason: string) => {
    const currentReasons = getValues('selected_reasons');
    const newReasons = currentReasons.includes(reason)
      ? currentReasons.filter(r => r !== reason)
      : [...currentReasons, reason];
    setValue('selected_reasons', newReasons);
  };

  const handleAddCustomReason = () => {
    if (!customReasonInput.trim()) return;

    const currentCustomReasons = getValues('custom_reasons');
    const newCustomReasons = [...currentCustomReasons, customReasonInput.trim()];
    setValue('custom_reasons', newCustomReasons);
    setCustomReasonInput('');
  };

  const handleRemoveCustomReason = (index: number) => {
    const currentCustomReasons = getValues('custom_reasons');
    const newCustomReasons = currentCustomReasons.filter((_, i) => i !== index);
    setValue('custom_reasons', newCustomReasons);
  };

  const handleNextStep = async () => {
    if (currentStep === 'stance') {
      setCurrentStep('reasons');
    } else if (currentStep === 'reasons') {
      setCurrentStep('location');
    } else if (currentStep === 'location') {
      // Generate message preview
      await handlePreviewMessage();
    } else if (currentStep === 'preview') {
      setCurrentStep('message');
    }
  };

  const handlePrevStep = () => {
    if (currentStep === 'message') {
      setCurrentStep('preview');
    } else if (currentStep === 'preview') {
      setCurrentStep('location');
    } else if (currentStep === 'location') {
      setCurrentStep('reasons');
    } else if (currentStep === 'reasons') {
      setCurrentStep('stance');
    }
  };

  const handlePreviewMessage = async () => {
    setIsLoadingPreview(true);
    setAiProgress(0);
    setAiProgressMessage('Looking up your representatives...');

    // Simulate progress updates to keep user engaged
    const progressInterval = setInterval(() => {
      setAiProgress(prev => {
        if (prev < 90) {
          const increment = Math.random() * 15 + 5; // Random increment between 5-20%
          const newProgress = Math.min(prev + increment, 90);

          // Update progress message based on progress
          if (newProgress < 30) {
            setAiProgressMessage('Looking up your representatives...');
          } else if (newProgress < 60) {
            setAiProgressMessage('Analyzing bill content...');
          } else if (newProgress < 85) {
            setAiProgressMessage('Crafting personalized messages...');
          } else {
            setAiProgressMessage('Finalizing your message...');
          }

          return newProgress;
        }
        return prev;
      });
    }, 300); // Update every 300ms for smooth progress

    try {
      const formData = getValues();
      const previewData: MessagePreviewRequest = {
        bill_id: bill.id,
        stance: formData.stance,
        selected_reasons: formData.selected_reasons,
        custom_reasons: formData.custom_reasons,
        zip_code: formData.zip_code
      };

      const result = await billActionApi.previewMessage(previewData);

      // Complete the progress
      setAiProgress(100);
      setAiProgressMessage('Message ready!');

      // Brief delay to show completion
      setTimeout(() => {
        setMessagePreview(result);
        setCurrentStep('preview');
        setAiProgress(0);
        setAiProgressMessage('');
      }, 500);

    } catch (error) {
      console.error('Failed to preview message:', error);

      // Show error state
      setAiProgress(0);
      setAiProgressMessage('');

      if (error.message?.includes('timeout')) {
        toast.error('AI personalization is taking longer than expected. Please try again.');
      } else {
        toast.error('Failed to generate message preview. Please try again.');
      }
    } finally {
      clearInterval(progressInterval);
      setIsLoadingPreview(false);
    }
  };

  const onFormSubmit = async (data: BillActionFormData) => {
    try {
      setIsSubmitting(true);

      const submitData: BillActionSubmitRequest = {
        bill_id: bill.id,
        stance: data.stance,
        selected_reasons: data.selected_reasons,
        custom_reasons: data.custom_reasons || [],
        custom_message: data.custom_message || undefined,
        zip_code: data.zip_code,
        email: data.email,
        address: data.address || undefined,
        city: data.city || undefined,
        state: data.state || undefined
      };

      const result = await billActionApi.submitBillAction(submitData);

      if (result.success) {
        // Check if we need to show Action Network form
        if (result.action_network_embed && result.delivery_summary.requires_user_completion) {
          setActionNetworkData(result);
          setCurrentStep('action_network');
          toast.success('Your message is ready! Please complete the Action Network form to send it to your representatives.');
        } else {
          // Legacy path for backward compatibility
          const totalMessages = result.message_personalization?.total_messages || result.officials_contacted || 0;
          toast.success(`Successfully sent your message to ${totalMessages} representative${totalMessages > 1 ? 's' : ''}!`);

          if (onSubmit) {
            onSubmit(result);
          }

          handleClose();
        }
      } else {
        toast.error('Failed to send your message. Please try again.');
      }

    } catch (error) {
      console.error('Failed to submit bill action:', error);
      toast.error('Failed to send your message. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const validateZipCode = (zip: string): boolean => {
    const zipRegex = /^\d{5}(-\d{4})?$/;
    return zipRegex.test(zip);
  };

  const getAvailableReasons = (): string[] => {
    if (!billActionData) return [];

    switch (watchedStance) {
      case 'support':
        return billActionData.support_reasons || [];
      case 'oppose':
        return billActionData.oppose_reasons || [];
      case 'amend':
        return billActionData.amend_reasons || [];
      default:
        return [];
    }
  };

  const canProceedFromReasons = watchedReasons.length > 0 || watchedCustomReasons.length > 0;
  const canSubmit = watchedZipCode && validateZipCode(watchedZipCode) && watchedEmail && /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(watchedEmail);

  if (isLoadingData) {
    return (
      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={() => {}}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                  <div className="flex items-center justify-center py-12">
                    <div className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-3 h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span className="text-lg text-gray-600">Loading bill information...</span>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    );
  }

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={handleClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-6xl transform rounded-2xl bg-white text-left align-middle shadow-xl transition-all h-[90vh] flex flex-col">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0">
                  <div>
                    <Dialog.Title as="h3" className="text-lg font-semibold leading-6 text-gray-900">
                      Take Action on {bill.bill_number}
                    </Dialog.Title>
                    <p className="text-sm text-gray-600 mt-1">{bill.title}</p>
                  </div>
                  <button
                    type="button"
                    className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    onClick={handleClose}
                  >
                    <span className="sr-only">Close</span>
                    <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
                </div>

                {/* Progress Steps */}
                <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    {['stance', 'reasons', 'location', 'preview', 'message'].map((step, index) => (
                      <div key={step} className="flex items-center">
                        <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                          currentStep === step
                            ? 'bg-blue-600 text-white'
                            : index < ['stance', 'reasons', 'location', 'preview', 'message'].indexOf(currentStep)
                            ? 'bg-green-600 text-white'
                            : 'bg-gray-300 text-gray-600'
                        }`}>
                          {index < ['stance', 'reasons', 'location', 'preview', 'message'].indexOf(currentStep) ? (
                            <CheckIcon className="w-5 h-5" />
                          ) : (
                            index + 1
                          )}
                        </div>
                        <span className={`ml-2 text-sm font-medium ${
                          currentStep === step ? 'text-blue-600' : 'text-gray-500'
                        }`}>
                          {step.charAt(0).toUpperCase() + step.slice(1)}
                        </span>
                        {index < 4 && (
                          <div className={`w-12 h-0.5 mx-4 ${
                            index < ['stance', 'reasons', 'location', 'preview', 'message'].indexOf(currentStep)
                              ? 'bg-green-600'
                              : 'bg-gray-300'
                          }`} />
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                <form onSubmit={handleSubmit(onFormSubmit)} className="flex flex-col flex-1 min-h-0">
                  {/* Scrollable Content Area */}
                  <div className="flex-1 overflow-y-auto min-h-0">
                    {/* Bill Details - Always Visible */}
                    <div className="p-6 border-b border-gray-200">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Bill Details & Analysis</h3>
                      <BillDetailsContent bill={bill} />
                    </div>

                    {/* Step Content */}
                    <div className="p-6">
                      {/* Step 1: Stance Selection */}
                      {currentStep === 'stance' && (
                        <div className="space-y-6">
                          <div>
                            <h4 className="text-lg font-medium text-gray-900 mb-4">
                              What is your position on this bill?
                            </h4>

                            {billActionData?.ai_summary && (
                              <div className="mb-6 p-4 bg-blue-50 rounded-lg">
                                <h5 className="font-medium text-blue-900 mb-2">AI Summary</h5>
                                <p className="text-sm text-blue-800">{billActionData.ai_summary}</p>
                              </div>
                            )}

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            {(['support', 'oppose', 'amend'] as const).map((stance) => (
                              <button
                                key={stance}
                                type="button"
                                onClick={() => handleStanceSelect(stance)}
                                className={`p-6 border-2 rounded-lg text-left transition-all ${
                                  watchedStance === stance
                                    ? 'border-blue-500 bg-blue-50'
                                    : 'border-gray-200 hover:border-gray-300'
                                }`}
                              >
                                <div className="flex items-center mb-2">
                                  <div className={`w-4 h-4 rounded-full border-2 mr-3 ${
                                    watchedStance === stance
                                      ? 'border-blue-500 bg-blue-500'
                                      : 'border-gray-300'
                                  }`}>
                                    {watchedStance === stance && (
                                      <div className="w-full h-full rounded-full bg-white scale-50"></div>
                                    )}
                                  </div>
                                  <h5 className="font-medium text-gray-900 capitalize">
                                    {stance} This Bill
                                  </h5>
                                </div>
                                <p className="text-sm text-gray-600">
                                  {stance === 'support' && 'I believe this bill should be passed as written'}
                                  {stance === 'oppose' && 'I believe this bill should not be passed'}
                                  {stance === 'amend' && 'I support the concept but believe changes are needed'}
                                </p>
                              </button>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Step 2: Reason Selection */}
                    {currentStep === 'reasons' && (
                      <div className="space-y-6">
                        <div>
                          <h4 className="text-lg font-medium text-gray-900 mb-4">
                            Why do you {watchedStance} this bill?
                          </h4>
                          <p className="text-sm text-gray-600 mb-6">
                            Select the reasons that best represent your position. These will be included in your message.
                          </p>

                          <div className="space-y-3">
                            {getAvailableReasons().map((reason, index) => (
                              <label
                                key={index}
                                className="flex items-start p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                              >
                                <input
                                  type="checkbox"
                                  checked={watchedReasons.includes(reason)}
                                  onChange={() => handleReasonToggle(reason)}
                                  className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <span className="ml-3 text-sm text-gray-700">{reason}</span>
                              </label>
                            ))}
                          </div>

                          {getAvailableReasons().length === 0 && (
                            <div className="text-center py-8">
                              <p className="text-gray-500">No reasons available for this stance.</p>
                            </div>
                          )}
                        </div>

                        {/* Custom Reasons Section */}
                        <div>
                          <h5 className="text-md font-medium text-gray-900 mb-3">
                            Add your own reasons
                          </h5>
                          <p className="text-sm text-gray-600 mb-4">
                            Have a specific reason not listed above? Add your own.
                          </p>

                          <div className="flex gap-2 mb-4">
                            <input
                              type="text"
                              value={customReasonInput}
                              onChange={(e) => setCustomReasonInput(e.target.value)}
                              placeholder="Enter your custom reason..."
                              className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                              onKeyPress={(e) => {
                                if (e.key === 'Enter') {
                                  e.preventDefault();
                                  handleAddCustomReason();
                                }
                              }}
                            />
                            <button
                              type="button"
                              onClick={handleAddCustomReason}
                              disabled={!customReasonInput.trim()}
                              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                            >
                              Add
                            </button>
                          </div>

                          {/* Display custom reasons */}
                          {watchedCustomReasons.length > 0 && (
                            <div className="space-y-2">
                              {watchedCustomReasons.map((reason, index) => (
                                <div
                                  key={index}
                                  className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg"
                                >
                                  <span className="text-sm text-blue-900">{reason}</span>
                                  <button
                                    type="button"
                                    onClick={() => handleRemoveCustomReason(index)}
                                    className="text-blue-600 hover:text-blue-800"
                                  >
                                    <XMarkIcon className="w-4 h-4" />
                                  </button>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Step 3: Location Information */}
                    {currentStep === 'location' && (
                      <div className="space-y-6">
                        <div>
                          <h4 className="text-lg font-medium text-gray-900 mb-4">
                            Enter your location to find your representatives
                          </h4>
                          <p className="text-sm text-gray-600 mb-6">
                            We'll use this information to identify your representatives and personalize your message.
                          </p>

                          <div className="space-y-4">
                            <div>
                              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                                Email Address *
                              </label>
                              <input
                                {...register('email', {
                                  required: 'Email address is required',
                                  pattern: {
                                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                                    message: 'Please enter a valid email address'
                                  }
                                })}
                                type="email"
                                placeholder="<EMAIL>"
                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                              />
                              {errors.email && (
                                <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
                              )}
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <label htmlFor="zip_code" className="block text-sm font-medium text-gray-700 mb-1">
                                  ZIP Code *
                                </label>
                                <input
                                  {...register('zip_code', {
                                    required: 'ZIP code is required',
                                    pattern: {
                                      value: /^\d{5}(-\d{4})?$/,
                                      message: 'Please enter a valid ZIP code'
                                    }
                                  })}
                                  type="text"
                                  placeholder="12345"
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                />
                                {errors.zip_code && (
                                  <p className="mt-1 text-sm text-red-600">{errors.zip_code.message}</p>
                                )}
                              </div>

                              <div>
                                <label htmlFor="state" className="block text-sm font-medium text-gray-700 mb-1">
                                  State
                                </label>
                                <input
                                  {...register('state')}
                                  type="text"
                                  placeholder="CA"
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                />
                              </div>
                            </div>
                          </div>

                            <div>
                              <label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-1">
                                City
                              </label>
                              <input
                                {...register('city')}
                                type="text"
                                placeholder="San Francisco"
                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                              />
                            </div>

                            <div>
                              <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                                Street Address
                              </label>
                              <input
                                {...register('address')}
                                type="text"
                                placeholder="123 Main St"
                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                              />
                            </div>
                          </div>

                          {/* AI Progress Indicator */}
                          {isLoadingPreview && (
                            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                              <div className="flex items-center mb-3">
                                <SparklesIcon className="w-5 h-5 text-blue-600 mr-2" />
                                <span className="text-sm font-medium text-blue-900">AI Personalization in Progress</span>
                              </div>

                              <div className="w-full bg-blue-200 rounded-full h-2 mb-2">
                                <div
                                  className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                                  style={{ width: `${aiProgress}%` }}
                                ></div>
                              </div>

                              <p className="text-sm text-blue-800">{aiProgressMessage}</p>

                              <div className="mt-3 text-xs text-blue-700">
                                <p>✨ Analyzing your position and reasons</p>
                                <p>🎯 Crafting personalized messages for each representative</p>
                                <p>📝 Ensuring professional tone and political effectiveness</p>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Step 4: Message Preview */}
                    {currentStep === 'preview' && messagePreview && (
                      <div className="space-y-6">
                        <div>
                          <h4 className="text-lg font-medium text-gray-900 mb-4">
                            Review your personalized messages
                          </h4>
                          <p className="text-sm text-gray-600 mb-6">
                            We&apos;ve found {messagePreview.representatives?.length || 0} representative{(messagePreview.representatives?.length || 0) > 1 ? 's' : ''} for your area and crafted personalized messages.
                          </p>

                          {/* Representatives and Messages */}
                          <div className="space-y-4">
                            {messagePreview.representatives?.map((rep, index) => {
                              const message = messagePreview.personalized_messages?.[index];
                              return (
                                <div key={index} className="border border-gray-200 rounded-lg p-4">
                                  <div className="flex items-center mb-3">
                                    <div className="flex-1">
                                      <h5 className="font-medium text-gray-900">{rep.name}</h5>
                                      <p className="text-sm text-gray-600">
                                        {rep.title} • {rep.party} • {rep.state}
                                      </p>
                                    </div>
                                  </div>

                                  {message && (
                                    <div className="bg-gray-50 rounded-lg p-3">
                                      <h6 className="font-medium text-sm text-gray-900 mb-2">
                                        Subject: {message.subject}
                                      </h6>
                                      <div className="text-sm text-gray-700 whitespace-pre-wrap">
                                        {message.body}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              );
                            })}
                          </div>

                          {/* Summary */}
                          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                            <h5 className="font-medium text-green-900 mb-2">Ready to Send</h5>
                            <p className="text-sm text-green-800">
                              Your messages are ready to be sent to {messagePreview.representatives?.length || 0} representative{(messagePreview.representatives?.length || 0) > 1 ? 's' : ''}.
                              You can proceed to send them as-is, or customize them further in the next step.
                            </p>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Step 5: Message Editing */}
                    {currentStep === 'message' && messagePreview && (
                      <div className="space-y-6">
                        <div>
                          <h4 className="text-lg font-medium text-gray-900 mb-4">
                            Customize your message (optional)
                          </h4>
                          <p className="text-sm text-gray-600 mb-6">
                            You can edit the message below or send the AI-generated version as-is.
                          </p>

                          <div>
                            <label htmlFor="custom_message" className="block text-sm font-medium text-gray-700 mb-2">
                              Custom Message
                            </label>
                            <textarea
                              {...register('custom_message')}
                              rows={8}
                              placeholder="Enter your custom message here, or leave blank to use the AI-generated messages..."
                              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            />
                            <p className="mt-1 text-xs text-gray-500">
                              If you provide a custom message, it will be sent to all representatives instead of the personalized AI messages.
                            </p>
                          </div>

                          {/* Final Summary */}
                          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h5 className="font-medium text-blue-900 mb-2">Final Review</h5>
                            <div className="text-sm text-blue-800 space-y-1">
                              <p><strong>Position:</strong> {watchedStance.charAt(0).toUpperCase() + watchedStance.slice(1)} this bill</p>
                              <p><strong>Recipients:</strong> {messagePreview.representatives?.length || 0} representative{(messagePreview.representatives?.length || 0) > 1 ? 's' : ''}</p>
                              <p><strong>Reasons:</strong> {[...watchedReasons, ...watchedCustomReasons].length} selected</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Step 6: Action Network Form */}
                    {currentStep === 'action_network' && actionNetworkData && (
                      <div className="space-y-6">
                        <div>
                          <h4 className="text-lg font-medium text-gray-900 mb-4">
                            Complete Your Message Delivery
                          </h4>
                          <p className="text-sm text-gray-600 mb-6">
                            Your personalized message is ready! Complete the form below to send it directly to your representatives.
                          </p>

                          {/* Action Network Embed Info */}
                          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                            <h5 className="font-medium text-blue-900 mb-2">Message Summary</h5>
                            <div className="text-sm text-blue-800 space-y-1">
                              <p><strong>Recipients:</strong> {actionNetworkData.officials_contacted} representative{actionNetworkData.officials_contacted > 1 ? 's' : ''}</p>
                              <p><strong>Chamber:</strong> {actionNetworkData.action_network_embed.chamber === 'senate' ? 'U.S. Senate' : 'U.S. House of Representatives'}</p>
                              <p><strong>Message Preview:</strong> {actionNetworkData.personalized_message_preview}</p>
                            </div>
                          </div>

                          {/* Action Network Iframe */}
                          <div className="border border-gray-300 rounded-lg overflow-hidden">
                            <iframe
                              src={actionNetworkData.action_network_embed.iframe_url}
                              width="100%"
                              height="600"
                              frameBorder="0"
                              title="Action Network Form"
                              className="w-full"
                            />
                          </div>

                          {/* Instructions */}
                          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                            <h5 className="font-medium text-green-900 mb-2">Next Steps</h5>
                            <p className="text-sm text-green-800">
                              {actionNetworkData.user_instructions?.next_step || 'Complete the form above to send your message to your representatives.'}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                    </div>
                  </div>

                  {/* Fixed Navigation Buttons */}
                  <div className="flex-shrink-0 flex justify-between p-6 border-t border-gray-200 bg-white shadow-lg">
                    <button
                      type="button"
                      onClick={handlePrevStep}
                      disabled={currentStep === 'stance' || currentStep === 'action_network'}
                      className={`px-4 py-2 text-sm font-medium rounded-md ${
                        currentStep === 'stance' || currentStep === 'action_network'
                          ? 'text-gray-400 cursor-not-allowed'
                          : 'text-gray-700 bg-gray-100 hover:bg-gray-200'
                      }`}
                    >
                      Previous
                    </button>

                    {currentStep === 'action_network' ? (
                      <button
                        type="button"
                        onClick={handleClose}
                        className="px-6 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-md"
                      >
                        Done
                      </button>
                    ) : currentStep === 'message' ? (
                      <button
                        type="submit"
                        disabled={isSubmitting}
                        className={`px-6 py-2 text-sm font-medium text-white rounded-md ${
                          isSubmitting
                            ? 'bg-gray-400 cursor-not-allowed'
                            : 'bg-blue-600 hover:bg-blue-700'
                        }`}
                      >
                        {isSubmitting ? 'Preparing...' : 'Prepare Messages'}
                      </button>
                    ) : (
                      <button
                        type="button"
                        onClick={handleNextStep}
                        disabled={
                          (currentStep === 'reasons' && !canProceedFromReasons) ||
                          (currentStep === 'location' && (!canSubmit || isLoadingPreview))
                        }
                        className={`px-4 py-2 text-sm font-medium text-white rounded-md ${
                          (currentStep === 'reasons' && !canProceedFromReasons) ||
                          (currentStep === 'location' && (!canSubmit || isLoadingPreview))
                            ? 'bg-gray-400 cursor-not-allowed'
                            : 'bg-blue-600 hover:bg-blue-700'
                        }`}
                      >
                        {currentStep === 'location' && isLoadingPreview ? (
                          <span className="flex items-center gap-2">
                            <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                            Crafting Message...
                          </span>
                        ) : 'Next'}
                      </button>
                    )}
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

// Bill Details Content Component
interface BillDetailsContentProps {
  bill: Bill;
}

const BillDetailsContent: React.FC<BillDetailsContentProps> = ({ bill }) => {
  return (
    <div className="space-y-6">
      {/* Basic Bill Information - Always show */}
      <div className="bg-blue-50 rounded-lg p-4 border-l-4 border-blue-500">
        <h4 className="flex items-center gap-2 font-bold text-lg text-blue-900 mb-3">
          <span>📋</span>
          Bill Information
        </h4>
        <div className="space-y-2 text-blue-800">
          <p><strong>Bill Number:</strong> {bill.bill_number}</p>
          <p><strong>Status:</strong> {bill.status}</p>
          <p><strong>Chamber:</strong> {bill.chamber}</p>
          {bill.sponsor_name && <p><strong>Sponsor:</strong> {bill.sponsor_name} ({bill.sponsor_party})</p>}
          {bill.simple_summary && (
            <div className="mt-3">
              <strong>Simple Summary:</strong>
              <p className="mt-1 text-sm">{bill.simple_summary}</p>
            </div>
          )}
          {bill.ai_summary && (
            <div className="mt-3">
              <strong>Summary:</strong>
              <p className="mt-1 text-sm">{bill.ai_summary}</p>
            </div>
          )}
        </div>
      </div>

      {/* What This Bill Does */}
      {bill.summary_what_does && (
        <DetailSection
          title={bill.summary_what_does.title || "What This Bill Does"}
          icon="📋"
          color="blue"
          content={bill.summary_what_does.content}
          keyPoints={bill.summary_what_does.key_points}
        />
      )}

      {/* Who This Affects */}
      {bill.summary_who_affects && (
        <DetailSection
          title={bill.summary_who_affects.title || "Who This Affects"}
          icon="👥"
          color="green"
          content={bill.summary_who_affects.content}
          groups={bill.summary_who_affects.affected_groups}
        />
      )}

      {/* Why It Matters - Benefits & Concerns */}
      {bill.summary_why_matters && (
        <div className="bg-purple-50 rounded-lg p-4 border-l-4 border-purple-500">
          <h4 className="flex items-center gap-2 font-bold text-lg text-purple-900 mb-3">
            <span>💡</span>
            {bill.summary_why_matters.title || "Why It Matters to You"}
          </h4>
          <p className="text-purple-800 mb-4">{bill.summary_why_matters.content}</p>

          {bill.summary_why_matters.benefits && bill.summary_why_matters.benefits.length > 0 && (
            <div className="mb-3">
              <h5 className="font-semibold text-green-700 mb-2">✅ Potential Benefits:</h5>
              <ul className="list-disc ml-5 space-y-1">
                {bill.summary_why_matters.benefits.map((benefit, index) => (
                  <li key={index} className="text-green-700 text-sm">{benefit}</li>
                ))}
              </ul>
            </div>
          )}

          {bill.summary_why_matters.concerns && bill.summary_why_matters.concerns.length > 0 && (
            <div>
              <h5 className="font-semibold text-red-700 mb-2">⚠️ Potential Concerns:</h5>
              <ul className="list-disc ml-5 space-y-1">
                {bill.summary_why_matters.concerns.map((concern, index) => (
                  <li key={index} className="text-red-700 text-sm">{concern}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Key Provisions */}
      {bill.summary_key_provisions && (
        <DetailSection
          title={bill.summary_key_provisions.title || "Key Provisions"}
          icon="⚖️"
          color="indigo"
          content={bill.summary_key_provisions.content}
          provisions={bill.summary_key_provisions.provisions}
        />
      )}

      {/* Fallback for bills without structured summaries */}
      {!bill.summary_what_does && !bill.summary_who_affects && !bill.summary_why_matters && bill.ai_summary && (
        <div className="bg-blue-50 rounded-lg p-4 border-l-4 border-blue-500">
          <h4 className="flex items-center gap-2 font-bold text-blue-900 mb-2">
            <span>📋</span>
            Bill Summary
          </h4>
          <div className="text-blue-800 text-sm whitespace-pre-line">
            {bill.ai_summary}
          </div>
        </div>
      )}

      {/* Fallback for bills with no AI data at all */}
      {!bill.summary_what_does && !bill.summary_who_affects && !bill.summary_why_matters && !bill.ai_summary && (
        <div className="bg-gray-50 rounded-lg p-4 border-l-4 border-gray-400">
          <h4 className="flex items-center gap-2 font-bold text-gray-700 mb-2">
            <span>⚠️</span>
            No AI Analysis Available
          </h4>
          <p className="text-gray-600 text-sm">
            This bill hasn't been processed by our AI analysis system yet. Check back later for detailed summaries and insights.
          </p>
        </div>
      )}
    </div>
  );
};

// Detail Section Component for structured display
interface DetailSectionProps {
  title: string;
  icon: string;
  color: string;
  content: string;
  keyPoints?: string[];
  groups?: string[];
  provisions?: string[];
}

const DetailSection: React.FC<DetailSectionProps> = ({
  title,
  icon,
  color,
  content,
  keyPoints,
  groups,
  provisions
}) => (
  <div className={`bg-${color}-50 rounded-lg p-4 border-l-4 border-${color}-500`}>
    <h4 className={`flex items-center gap-2 font-bold text-lg text-${color}-900 mb-3`}>
      <span>{icon}</span>
      {title}
    </h4>
    <p className={`text-${color}-800 mb-3`}>{content}</p>

    {keyPoints && keyPoints.length > 0 && (
      <ul className="list-disc ml-5 space-y-1">
        {keyPoints.map((point, index) => (
          <li key={index} className={`text-${color}-700 text-sm`}>{point}</li>
        ))}
      </ul>
    )}

    {groups && groups.length > 0 && (
      <div className="flex flex-wrap gap-2 mt-3">
        {groups.map((group, index) => (
          <span key={index} className={`bg-${color}-200 text-${color}-800 px-2 py-1 rounded-full text-xs`}>
            {group}
          </span>
        ))}
      </div>
    )}

    {provisions && provisions.length > 0 && (
      <ul className="list-disc ml-5 space-y-1 mt-3">
        {provisions.map((provision, index) => (
          <li key={index} className={`text-${color}-700 text-sm`}>{provision}</li>
        ))}
      </ul>
    )}
  </div>
);

export default BillActionModal;
