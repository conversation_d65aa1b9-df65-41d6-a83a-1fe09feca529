'use client';

import { useState } from 'react';
import { toast } from 'react-hot-toast';

export default function AdminPage() {
  const [isProcessing, setIsProcessing] = useState(false);
  const [billId, setBillId] = useState('hr9-118');
  const [results, setResults] = useState<any>(null);

  const handlePullBill = async () => {
    setIsProcessing(true);
    setResults(null);

    try {
      // Parse bill ID (e.g., "hr9-118" -> congress=118, bill_type=hr, bill_number=9)
      const match = billId.match(/^([a-z]+)(\d+)-(\d+)$/i);
      if (!match) {
        throw new Error('Invalid bill ID format. Use format like: hr9-118, s1234-118');
      }

      const [, billType, billNumber, congress] = match;

      const response = await fetch(`http://localhost:8000/api/v1/admin/process-and-save-bill?congress=${congress}&bill_type=${billType}&bill_number=${billNumber}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      setResults(result);
      toast.success('Bill processed successfully!');
    } catch (error) {
      console.error('Error processing bill:', error);
      toast.error('Failed to process bill');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleTestPreview = async () => {
    setIsProcessing(true);
    
    try {
      const response = await fetch('http://localhost:8000/api/v1/actions/preview-message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bill_id: results?.bill_id || '25177ae6-9916-456b-aec2-7d3b84f87647', // Use processed bill ID or fallback
          stance: 'support',
          selected_reasons: ['This bill improves safety standards'],
          custom_reasons: ['My custom reason for supporting this'],
          zip_code: '60302'
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      setResults(result);
      toast.success('Message preview generated successfully!');
    } catch (error) {
      console.error('Error generating preview:', error);
      toast.error('Failed to generate message preview');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
            <p className="mt-1 text-sm text-gray-600">
              Test bill processing and AI summarization
            </p>
          </div>

          <div className="p-6 space-y-6">
            {/* Bill ID Input */}
            <div>
              <label htmlFor="billId" className="block text-sm font-medium text-gray-700 mb-2">
                Bill ID
              </label>
              <input
                type="text"
                id="billId"
                value={billId}
                onChange={(e) => setBillId(e.target.value)}
                placeholder="hr9-118"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-4">
              <button
                onClick={handlePullBill}
                disabled={isProcessing || !billId}
                className={`px-4 py-2 text-sm font-medium text-white rounded-md ${
                  isProcessing || !billId
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
              >
                {isProcessing ? 'Processing...' : 'Pull & Process Bill'}
              </button>

              <button
                onClick={handleTestPreview}
                disabled={isProcessing || !billId}
                className={`px-4 py-2 text-sm font-medium text-white rounded-md ${
                  isProcessing || !billId
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-green-600 hover:bg-green-700'
                }`}
              >
                {isProcessing ? 'Processing...' : 'Test Message Preview'}
              </button>
            </div>

            {/* Results Display */}
            {results && (
              <div className="mt-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Results</h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <pre className="text-sm text-gray-700 whitespace-pre-wrap overflow-auto max-h-96">
                    {JSON.stringify(results, null, 2)}
                  </pre>
                </div>
              </div>
            )}

            {/* Instructions */}
            <div className="mt-8 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">Instructions:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• <strong>Pull & Process Bill:</strong> Fetches bill data from Congress.gov and generates AI summary</li>
                <li>• <strong>Test Message Preview:</strong> Tests the message preview functionality with sample data</li>
                <li>• Use bill IDs like: hr9-118, s1-118, hr1234-118</li>
                <li>• Check the browser console for detailed error messages</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
