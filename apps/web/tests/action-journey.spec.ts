// Playwright E2E tests for the complete action journey including AI assist and Twitter integration
import { test, expect } from '@playwright/test';

test.describe('Action Journey - AI Assist & Twitter Integration', () => {
  // Mock API responses for consistent testing
  test.beforeEach(async ({ page }) => {
    // Mock the campaigns API
    await page.route('**/api/v1/campaigns*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 'test-campaign-1',
            title: 'Climate Action Now Act',
            description: 'Support renewable energy legislation to combat climate change',
            call_to_action: 'Please support the Climate Action Now Act to ensure a sustainable future for our children.',
            talking_points: [
              'Climate change is a critical issue requiring immediate action',
              'Renewable energy creates jobs and reduces pollution',
              'Future generations depend on our action today'
            ],
            status: 'active',
            campaign_type: 'support',
            is_featured: true,
            goal_actions: 1000,
            actual_actions: 250,
            completion_percentage: 25
          }
        ])
      });
    });

    // Mock the officials API
    await page.route('**/api/v1/officials*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 'official-1',
            name: 'Senator <PERSON>',
            level: 'federal',
            chamber: 'senate',
            party: 'D',
            state: 'CA',
            email: '<EMAIL>',
            phone: '(*************',
            photo_url: 'https://example.com/jane-smith.jpg',
            twitter_handle: 'senator_smith',
            office_address: '123 Capitol Hill, Washington, DC 20510'
          },
          {
            id: 'official-2',
            name: 'Representative John Doe',
            level: 'federal',
            chamber: 'house',
            party: 'R',
            state: 'CA',
            district: '12',
            email: '<EMAIL>',
            phone: '(*************',
            twitter_handle: 'rep_johndoe',
            office_address: '456 Capitol Hill, Washington, DC 20515'
          }
        ])
      });
    });

    // Mock the AI personalization endpoint
    await page.route('**/api/v1/ai/personalize-message', async (route) => {
      const request = route.request();
      const postData = request.postDataJSON();
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          personalized_message: `As a concerned citizen, I urge you to support the Climate Action Now Act. ${postData.raw_text} This legislation represents a critical step toward ensuring a sustainable environment for future generations. Please vote in favor of this important bill.`,
          original_length: postData.raw_text.length,
          personalized_length: 200,
          processing_time_ms: 1250.5
        })
      });
    });

    // Mock the actions endpoint to capture the payload
    await page.route('**/api/v1/actions', async (route) => {
      const request = route.request();
      const postData = request.postDataJSON();
      
      // Store the payload for verification
      await page.evaluate((data) => {
        window.lastActionPayload = data;
      }, postData);
      
      await route.fulfill({
        status: 202,
        contentType: 'application/json',
        body: JSON.stringify({
          id: 'action-123',
          status: 'pending',
          message: 'Action submitted successfully'
        })
      });
    });
  });

  test('complete action journey with AI assist and Twitter integration', async ({ page }) => {
    // Navigate to a campaign page (we'll mock this to show the action modal)
    await page.goto('/campaigns/test-campaign-1');

    // Wait for the page to load
    await page.waitForLoadState('networkidle');

    // Click the "Take Action" button to open the modal
    // Since we don't have the full campaign page, we'll simulate opening the modal
    await page.evaluate(() => {
      // Simulate the ActionModal being opened
      const event = new CustomEvent('openActionModal', {
        detail: {
          campaign: {
            id: 'test-campaign-1',
            title: 'Climate Action Now Act',
            description: 'Support renewable energy legislation to combat climate change',
            call_to_action: 'Please support the Climate Action Now Act to ensure a sustainable future for our children.',
            talking_points: [
              'Climate change is a critical issue requiring immediate action',
              'Renewable energy creates jobs and reduces pollution',
              'Future generations depend on our action today'
            ]
          },
          officials: [
            {
              id: 'official-1',
              name: 'Senator Jane Smith',
              level: 'federal',
              chamber: 'senate',
              party: 'D',
              state: 'CA',
              email: '<EMAIL>',
              twitter_handle: 'senator_smith'
            }
          ]
        }
      });
      window.dispatchEvent(event);
    });

    // For this test, we'll create a simple test page that includes the ActionModal
    await page.setContent(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Action Modal Test</title>
        <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
        <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
        <style>
          .modal-overlay { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); display: flex; align-items: center; justify-content: center; }
          .modal-content { background: white; padding: 20px; border-radius: 8px; max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto; }
          .form-group { margin-bottom: 15px; }
          .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
          .form-group input, .form-group textarea { width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
          .ai-section { background: linear-gradient(to right, #f3e8ff, #dbeafe); padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #c084fc; }
          .ai-section h4 { margin: 0 0 10px 0; color: #7c3aed; }
          .twitter-section { background: #dbeafe; padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #3b82f6; }
          .button { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px; }
          .button-primary { background: #3b82f6; color: white; }
          .button-secondary { background: #6b7280; color: white; }
          .button-ai { background: #7c3aed; color: white; }
          .button:disabled { background: #9ca3af; cursor: not-allowed; }
          .loading { opacity: 0.6; }
          .checkbox-group { display: flex; align-items: center; gap: 10px; }
        </style>
      </head>
      <body>
        <div id="root">
          <div class="modal-overlay">
            <div class="modal-content">
              <h2>Contact Your Representatives</h2>
              <p><strong>Campaign:</strong> Climate Action Now Act</p>
              <p>Support renewable energy legislation to combat climate change</p>
              
              <form id="actionForm">
                <div class="form-group">
                  <label for="email">Your Email Address *</label>
                  <input type="email" id="email" data-testid="action-modal-email-input" required>
                </div>
                
                <div class="ai-section">
                  <h4>🌟 AI Message Assistant</h4>
                  <p>Share your personal story or key concerns, and our AI will help craft a more persuasive message.</p>
                  <div class="form-group">
                    <textarea 
                      id="aiInput" 
                      data-testid="ai-assist-input" 
                      placeholder="e.g., I'm a parent concerned about climate change affecting my children's future..."
                      rows="3"
                    ></textarea>
                  </div>
                  <button type="button" id="generateButton" data-testid="ai-assist-generate-button" class="button button-ai">
                    ✨ Generate My Message
                  </button>
                </div>
                
                <div class="form-group">
                  <label for="message">Your Message *</label>
                  <textarea 
                    id="message" 
                    data-testid="action-modal-message-textarea" 
                    rows="8" 
                    required
                  >Please support the Climate Action Now Act to ensure a sustainable future for our children.</textarea>
                </div>
                
                <div class="twitter-section">
                  <div class="checkbox-group">
                    <input type="checkbox" id="twitterToggle" data-testid="action-modal-tweet-toggle">
                    <label for="twitterToggle">
                      <strong>Also post a public Tweet to this official</strong><br>
                      <small>Your message will be shared publicly on Twitter to increase visibility and pressure</small>
                    </label>
                    <span>🐦</span>
                  </div>
                </div>
                
                <div style="display: flex; justify-content: flex-end; gap: 10px; margin-top: 20px;">
                  <button type="button" data-testid="action-modal-cancel-button" class="button button-secondary">
                    Cancel
                  </button>
                  <button type="submit" data-testid="action-modal-send-button" class="button button-primary">
                    Send Message
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
        
        <script>
          // Simple form handling
          let isLoadingAI = false;
          
          document.getElementById('generateButton').addEventListener('click', async function() {
            const aiInput = document.getElementById('aiInput').value;
            const messageTextarea = document.getElementById('message');
            const button = this;
            
            if (!aiInput.trim()) return;
            
            isLoadingAI = true;
            button.disabled = true;
            button.textContent = '⏳ Generating...';
            
            try {
              const response = await fetch('/api/v1/ai/personalize-message', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  raw_text: aiInput,
                  context: 'Climate Action Now Act - Support renewable energy legislation',
                  tone: 'professional'
                })
              });
              
              const data = await response.json();
              messageTextarea.value = data.personalized_message;
              document.getElementById('aiInput').value = '';
            } catch (error) {
              console.error('AI generation failed:', error);
            } finally {
              isLoadingAI = false;
              button.disabled = false;
              button.textContent = '✨ Generate My Message';
            }
          });
          
          document.getElementById('actionForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const message = document.getElementById('message').value;
            const includeTwitter = document.getElementById('twitterToggle').checked;
            
            const actionTypes = ['EMAIL'];
            if (includeTwitter) {
              actionTypes.push('TWITTER');
            }
            
            const payload = {
              campaign_id: 'test-campaign-1',
              official_id: 'official-1',
              email: email,
              message: message,
              action_types: actionTypes
            };
            
            try {
              const response = await fetch('/api/v1/actions', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
              });
              
              if (response.status === 202) {
                alert('Action submitted successfully!');
              }
            } catch (error) {
              console.error('Action submission failed:', error);
            }
          });
        </script>
      </body>
      </html>
    `);

    // Wait for the page to be ready
    await page.waitForLoadState('domcontentloaded');

    // Step 1: Fill in the email field
    await page.fill('[data-testid="action-modal-email-input"]', '<EMAIL>');

    // Step 2: Test AI assistance
    await page.fill('[data-testid="ai-assist-input"]', 'I am a parent worried about the climate crisis affecting my children\'s future');
    
    // Step 3: Click "Generate My Message" button
    await page.click('[data-testid="ai-assist-generate-button"]');
    
    // Wait for AI generation to complete
    await page.waitForFunction(() => {
      const button = document.querySelector('[data-testid="ai-assist-generate-button"]');
      return button && !button.disabled && button.textContent.includes('Generate My Message');
    });
    
    // Step 4: Assert that the main message textarea is updated with AI-generated content
    const messageContent = await page.inputValue('[data-testid="action-modal-message-textarea"]');
    expect(messageContent).toContain('As a concerned citizen, I urge you to support the Climate Action Now Act');
    expect(messageContent).toContain('I am a parent worried about the climate crisis');
    expect(messageContent).toContain('This legislation represents a critical step');

    // Step 5: Click the "Post a Tweet" toggle
    await page.check('[data-testid="action-modal-tweet-toggle"]');
    
    // Verify the toggle is checked
    const isTwitterToggleChecked = await page.isChecked('[data-testid="action-modal-tweet-toggle"]');
    expect(isTwitterToggleChecked).toBe(true);

    // Step 6: Submit the action
    await page.click('[data-testid="action-modal-send-button"]');
    
    // Wait for the form submission to complete
    await page.waitForFunction(() => window.lastActionPayload != null);
    
    // Step 7: Assert that the POST /actions payload includes "TWITTER" in the action_types array
    const actionPayload = await page.evaluate(() => window.lastActionPayload);
    
    expect(actionPayload).toBeDefined();
    expect(actionPayload.action_types).toContain('EMAIL');
    expect(actionPayload.action_types).toContain('TWITTER');
    expect(actionPayload.email).toBe('<EMAIL>');
    expect(actionPayload.message).toContain('As a concerned citizen, I urge you to support the Climate Action Now Act');
    expect(actionPayload.campaign_id).toBe('test-campaign-1');
    expect(actionPayload.official_id).toBe('official-1');
  });

  test('action journey with email only (no Twitter)', async ({ page }) => {
    // Set up the same test page
    await page.setContent(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Action Modal Test</title>
        <style>
          .modal-overlay { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); display: flex; align-items: center; justify-content: center; }
          .modal-content { background: white; padding: 20px; border-radius: 8px; max-width: 600px; width: 90%; }
          .form-group { margin-bottom: 15px; }
          .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
          .form-group input, .form-group textarea { width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
          .twitter-section { background: #dbeafe; padding: 15px; border-radius: 8px; margin-bottom: 15px; }
          .button { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px; }
          .button-primary { background: #3b82f6; color: white; }
          .button-secondary { background: #6b7280; color: white; }
          .checkbox-group { display: flex; align-items: center; gap: 10px; }
        </style>
      </head>
      <body>
        <div class="modal-overlay">
          <div class="modal-content">
            <form id="actionForm">
              <div class="form-group">
                <input type="email" data-testid="action-modal-email-input" required>
              </div>
              <div class="form-group">
                <textarea data-testid="action-modal-message-textarea" required>Default message content</textarea>
              </div>
              <div class="twitter-section">
                <div class="checkbox-group">
                  <input type="checkbox" data-testid="action-modal-tweet-toggle">
                  <label>Also post a public Tweet</label>
                </div>
              </div>
              <button type="submit" data-testid="action-modal-send-button" class="button button-primary">Send</button>
            </form>
          </div>
        </div>
        
        <script>
          document.getElementById('actionForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.querySelector('[data-testid="action-modal-email-input"]').value;
            const message = document.querySelector('[data-testid="action-modal-message-textarea"]').value;
            const includeTwitter = document.querySelector('[data-testid="action-modal-tweet-toggle"]').checked;
            
            const actionTypes = ['EMAIL'];
            if (includeTwitter) {
              actionTypes.push('TWITTER');
            }
            
            const payload = {
              email: email,
              message: message,
              action_types: actionTypes
            };
            
            await fetch('/api/v1/actions', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(payload)
            });
          });
        </script>
      </body>
      </html>
    `);

    await page.waitForLoadState('domcontentloaded');

    // Fill form without checking Twitter toggle
    await page.fill('[data-testid="action-modal-email-input"]', '<EMAIL>');
    await page.fill('[data-testid="action-modal-message-textarea"]', 'My custom message');
    
    // Ensure Twitter toggle is NOT checked
    const isTwitterToggleChecked = await page.isChecked('[data-testid="action-modal-tweet-toggle"]');
    expect(isTwitterToggleChecked).toBe(false);

    // Submit the action
    await page.click('[data-testid="action-modal-send-button"]');
    
    // Wait for submission
    await page.waitForFunction(() => window.lastActionPayload != null);
    
    // Assert that only EMAIL is in action_types
    const actionPayload = await page.evaluate(() => window.lastActionPayload);
    expect(actionPayload.action_types).toEqual(['EMAIL']);
    expect(actionPayload.action_types).not.toContain('TWITTER');
  });

  test('AI assist input validation', async ({ page }) => {
    await page.setContent(`
      <!DOCTYPE html>
      <html>
      <head><title>AI Test</title></head>
      <body>
        <textarea data-testid="ai-assist-input"></textarea>
        <button data-testid="ai-assist-generate-button" disabled>Generate</button>
        <script>
          const input = document.querySelector('[data-testid="ai-assist-input"]');
          const button = document.querySelector('[data-testid="ai-assist-generate-button"]');
          
          input.addEventListener('input', function() {
            button.disabled = !this.value.trim();
          });
        </script>
      </body>
      </html>
    `);

    // Initially button should be disabled
    const isInitiallyDisabled = await page.isDisabled('[data-testid="ai-assist-generate-button"]');
    expect(isInitiallyDisabled).toBe(true);

    // Type in AI input
    await page.fill('[data-testid="ai-assist-input"]', 'Test input');
    
    // Button should now be enabled
    const isEnabledAfterInput = await page.isDisabled('[data-testid="ai-assist-generate-button"]');
    expect(isEnabledAfterInput).toBe(false);

    // Clear input
    await page.fill('[data-testid="ai-assist-input"]', '');
    
    // Button should be disabled again
    const isDisabledAfterClear = await page.isDisabled('[data-testid="ai-assist-generate-button"]');
    expect(isDisabledAfterClear).toBe(true);
  });
});
